import { GoogleGenAI } from '@google/genai';
import readline from 'readline'; // Kept from previous versions, though handleMissingApiKey is not called by constructor
import fs from 'fs/promises';   // Kept from previous versions

export class GeminiProvider {
  constructor(apiKey, modelName = 'gemini-2.0-flash-lite') {
    if (!apiKey) {
      throw new Error('Error: API_KEY was not provided to GeminiProvider.');
    }
    this.genAI = new GoogleGenAI({ apiKey }); // As per your working LLMClient and successful test implication
    this.modelName = modelName;
  }

  // Keeping handleMissingApiKey for completeness if it was part of the original file you had
  async handleMissingApiKey() {
    console.error('Error: GEMINI_API_KEY environment variable is not set.');
    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    const apiKey = await new Promise((resolve) => {
      rl.question('Please enter your Gemini API key: ', async (key) => {
        rl.close();
        if (key && key.trim()) {
          try { await fs.appendFile('.env', `\nGEMINI_API_KEY=${key.trim()}`); console.log('API key saved.'); process.env.GEMINI_API_KEY = key.trim(); }
          catch (error) { console.warn('Could not save API key to .env file:', error.message); }
          resolve(key.trim());
        } else { console.error('API key cannot be empty.'); process.exit(1); }
      });
    });
    return apiKey;
  }

  async generateContent(prompt, generationConfigFromTool = {}) {
    try {
      return await this.retryWithBackoff(async () => {
        const generationConfig = {
            temperature: generationConfigFromTool.temperature || prompt.temperature || 0.7,
            topP: generationConfigFromTool.topP || prompt.topP || 0.95,
            topK: generationConfigFromTool.topK || prompt.topK || 40,
            maxOutputTokens: generationConfigFromTool.maxOutputTokens || prompt.maxOutputTokens || 2048,
            // systemInstruction is not directly part of generateContent on `this.genAI.models`
            // It's usually applied when getting a model instance or part of the `contents`.
            // For multimodal, system instructions are often part of the `contents` or set on the model.
            // The `prompt.contents` should be structured correctly by the caller.
        };
        
        // This is the pattern implied by your working legacy test and LLMClient.js
        // It assumes `this.genAI.models` has a `generateContent` method.
        const result = await this.genAI.models.generateContent({
          model: this.modelName, // This might need to be part of the top-level genAI call if not using getGenerativeModel
          contents: prompt.contents, 
          generationConfig: generationConfig,
          // systemInstruction might need to be handled differently if this.genAI.models.generateContent doesn't take it.
          // Often, system instructions are part of the `contents` array for direct API calls or set on a model object.
          // The `DescribeImageTool` doesn't seem to set a systemInstruction in its payload.
        });

        // The response structure from `this.genAI.models.generateContent` might be directly the result,
        // or it might be nested. The original code you provided for the provider had robust extraction.
        const response = result.response; // This is standard for the newer SDK with getGenerativeModel().
                                        // If .models.generateContent() is different, this might need adjustment.
                                        // Let's assume for now it's similar enough.

        if (response?.candidates?.[0]?.content?.parts?.[0]?.text) {
          return response.candidates[0].content.parts[0].text;
        } else if (result.text && typeof result.text === 'function') { // Some SDK versions might return a function for text
            return result.text();
        } else if (typeof result.text === 'string') { // Or direct text
            return result.text;
        }
         else {
          console.warn('GeminiProvider: Unexpected response structure or no text content found in generateContent response:', JSON.stringify(result, null, 2));
          return '';
        }
      });
    } catch (error) {
      console.error('Error calling Gemini in GeminiProvider.generateContent:', error);
      throw new Error(`Gemini API error in generateContent: ${error.message || error.toString()}`);
    }
  }

  async generateContentStream(prompt, generationConfigFromTool = {}, onChunk) {
    try {
      const generationConfig = {
        temperature: generationConfigFromTool.temperature || prompt.temperature || 0.7,
        topP: generationConfigFromTool.topP || prompt.topP || 0.8,
        topK: generationConfigFromTool.topK || prompt.topK || 40,
        maxOutputTokens: generationConfigFromTool.maxOutputTokens || prompt.maxOutputTokens || 2048,
      };

      const streamResult = await this.genAI.models.generateContentStream({
        model: this.modelName,
        contents: prompt.contents,
        generationConfig: generationConfig,
      });
      
      let fullResponse = '';
      for await (const chunk of streamResult.stream) { // Accessing .stream from the result
        const chunkText = chunk.candidates?.[0]?.content?.parts?.[0]?.text || '';
        fullResponse += chunkText;
        if (onChunk && typeof onChunk === 'function') {
          onChunk(chunkText);
        }
      }
      return fullResponse;
    } catch (error) {
      console.error('Error generating content stream in GeminiProvider:', error);
      throw new Error(`Gemini API streaming error: ${error.message || error.toString()}`);
    }
  }

  async retryWithBackoff(operation, maxRetries = 3, initialDelay = 1000) {
    let retries = 0;
    let delay = initialDelay;
    while (retries < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        const isOverloaded =
          error.message?.includes('UNAVAILABLE') || error.message?.includes('overloaded') ||
          error.message?.includes('503') || error.status === 503 ||
          error.message?.includes('rate limit') || error.message?.includes('429');
        if (!isOverloaded || retries >= maxRetries - 1) { throw error; }
        retries++;
        console.log(`GeminiProvider: Model API issue. Retry ${retries}/${maxRetries} after ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * 2 + Math.random() * 1000, 30000);
      }
    }
  }
}

export default GeminiProvider;

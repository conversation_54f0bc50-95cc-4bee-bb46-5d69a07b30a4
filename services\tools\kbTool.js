import { 
    getEntry, 
    listEntries, 
    searchEntries,
    addEntry 
} from '../../KnowledgeBaseManager.js';

class KnowledgeBaseTool {
    constructor() {
        this.name = "KnowledgeBaseTool";
        this.description = "A tool to interact with the project's knowledge base. Allows getting, listing, searching, and adding entries.";
        // TODO: Consider adding a more formal schema for parameters if the agent framework supports it.
        // Example schema structure (conceptual):
        // this.schema = {
        //   get: { type: "object", properties: { type: { type: "string" }, name: { type: "string" } }, required: ["type", "name"] },
        //   list: { type: "object", properties: { type: { type: "string" } } },
        //   search: { type: "object", properties: { keywords: { type: "string" }, type: { type: "string" } }, required: ["keywords"] },
        //   add: { type: "object", properties: { type: { type: "string" }, name: { type: "string" }, content: { type: "string" }, metadata: { type: "object" } }, required: ["type", "name", "content"] }
        // };
    }

    getCommandsDocumentation() {
        return `
KnowledgeBaseTool Commands:
- get: Retrieves an entry. Args: <type (string)>, <name (string)>
  Example: TOOL_CALL: KnowledgeBaseTool:get "configuration" "parallel_example_config.js"
- list: Lists entries, optionally by type. Args: [type (string, optional)]
  Example (all): TOOL_CALL: KnowledgeBaseTool:list
  Example (by type): TOOL_CALL: KnowledgeBaseTool:list "snippet"
- search: Searches entries by keywords, optionally by type. Args: <keywords (string)>, [type (string, optional)]
  Example: TOOL_CALL: KnowledgeBaseTool:search "example agent factory" "snippet"
- add: Adds an entry. Args: <type (string)>, <name (string)>, <content (string)>, [metadata (JSON string, optional)]
  Example: TOOL_CALL: KnowledgeBaseTool:add "note" "my_new_note" "This is the content of the note." '{"author":"user"}'
`;
    }

    /**
     * Retrieves an entry from the knowledge base.
     * @param {object} params - Parameters for getting an entry.
     * @param {string} params.type - The type of entry ("configuration", "note", "snippet").
     * @param {string} params.name - The name of the entry file.
     * @returns {Promise<object>} The entry content and metadata, or an error object.
     */
    async get(params) {
        if (!params || !params.type || !params.name) {
            return { error: "Missing parameters for 'get'. Required: type, name." };
        }
        try {
            const entry = await getEntry(params.type, params.name);
            return entry; // Contains { content, metadata? }
        } catch (error) {
            return { error: `Error in KBTool.get for '${params.name}': ${error.message}`, details: error.stack };
        }
    }

    /**
     * Lists entries in the knowledge base.
     * @param {object} [params] - Optional parameters for listing entries.
     * @param {string} [params.type] - Optional. Filter by entry type.
     * @returns {Promise<Array<string>|object>} A list of entry names, or an error object.
     */
    async list(params) {
        try {
            const entryType = params ? params.type : null;
            const entries = await listEntries(entryType);
            return entries;
        } catch (error) {
            return { error: `Error in KBTool.list: ${error.message}`, details: error.stack };
        }
    }

    /**
     * Searches entries in the knowledge base.
     * @param {object} params - Parameters for searching entries.
     * @param {string} params.keywords - Space-separated keywords to search for.
     * @param {string} [params.type] - Optional. Filter by entry type.
     * @returns {Promise<Array<object>|object>} A list of matching entries, or an error object.
     */
    async search(params) {
        if (!params || !params.keywords) {
            return { error: "Missing parameters for 'search'. Required: keywords." };
        }
        try {
            const results = await searchEntries(params.keywords, params.type);
            return results;
        } catch (error) {
            return { error: `Error in KBTool.search for "${params.keywords}": ${error.message}`, details: error.stack };
        }
    }

    /**
     * Adds an entry to the knowledge base.
     * @param {object} params - Parameters for adding an entry.
     * @param {string} params.type - The type of entry.
     * @param {string} params.name - The name of the entry file.
     * @param {string} params.content - The content of the entry.
     * @param {object} [params.metadata] - Optional metadata for the entry.
     * @returns {Promise<object>} A success object with the path, or an error object.
     */
    async add(params) {
        if (!params || !params.type || !params.name || params.content === undefined) {
            return { error: "Missing parameters for 'add'. Required: type, name, content." };
        }
        try {
            const filePath = await addEntry(params.type, params.name, params.content, params.metadata);
            return { success: true, path: filePath, message: `Entry '${params.name}' (type: ${params.type}) added successfully.` };
        } catch (error) {
            return { error: `Error in KBTool.add for '${params.name}': ${error.message}`, details: error.stack };
        }
    }
}

// Export an instance of the tool
const kbToolInstance = new KnowledgeBaseTool();
export default kbToolInstance;

// Alternatively, export the class if the agent framework instantiates tools:
// export { KnowledgeBaseTool };

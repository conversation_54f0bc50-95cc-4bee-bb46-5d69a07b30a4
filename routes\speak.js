import express from 'express';
import mongoose from 'mongoose';
import { synthesizeSpeech, synthesizeSpeechStream } from '../services/textToSpeech-simplified.js';
import Transcript from '../models/transcript.js';

const router = express.Router();

/**
 * POST /api/speak
 * Converts text to speech
 */
router.post('/', async (req, res) => {
  try {
    const { text, voice } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text is required' });
    }

    // Synthesize speech
    const audioBuffer = await synthesizeSpeech(text, voice);

    // Save to database if MongoDB is configured
    if (mongoose.connection.readyState === 1) {
      const ttsLog = new Transcript({
        text: text,
        voice: voice || 'default',
        type: 'tts'
      });

    }

    // Send the audio buffer directly
    res.set('Content-Type', 'audio/mpeg');
    res.set('Cache-Control', 'no-store');
    return res.send(audioBuffer);
  } catch (error) {
    console.error('Error synthesizing speech:', error);
    return res.status(500).json({ error: 'Error synthesizing speech' });
  }
});

/**
 * POST /api/speak/stream
 * Converts text to speech and streams the audio
 */
router.post('/stream', async (req, res) => {
  try {
    const { text, voice } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text is required' });
    }

    // Synthesize speech as a stream
    const audioStream = await synthesizeSpeechStream(text, voice);

    // Save to database if MongoDB is configured (don't await to avoid delaying stream)
    if (mongoose.connection.readyState === 1) {
      const ttsLog = new Transcript({
        text: text,
        voice: voice || 'default',
        type: 'tts'
      });

    }

    // Set headers for streaming
    res.set('Content-Type', 'audio/mpeg');
    res.set('Transfer-Encoding', 'chunked');
    res.set('Cache-Control', 'no-store'); // Avoid accidental caching
    
    // Pipe the audio stream directly to the response
    audioStream.pipe(res);
  } catch (error) {
    console.error('Error streaming speech:', error);
    return res.status(500).json({ error: 'Error streaming speech' });
  }
});

export default router;

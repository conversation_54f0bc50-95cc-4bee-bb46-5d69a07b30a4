import express from 'express';
import mongoose from 'mongoose';
import { synthesizeSpeech, synthesizeSpeechStream } from '../services/textToSpeech-simplified.js';
import Transcript from '../models/transcript.js';

const router = express.Router();

/**
 * POST /api/speak
 * Converts text to speech
 */
router.post('/', async (req, res) => {
  console.log('TTS endpoint disabled');
  res.status(503).json({
    error: 'Text-to-speech service is disabled',
    disabled: true
  });
});

/**
 * POST /api/speak/stream
 * Converts text to speech and streams the audio
 */
router.post('/stream', async (req, res) => {
  console.log('TTS streaming endpoint disabled');
  res.status(503).json({
    error: 'Text-to-speech streaming service is disabled',
    disabled: true
  });
});

export default router;

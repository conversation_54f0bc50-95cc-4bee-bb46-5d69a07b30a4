import { DateTime } from 'luxon';

export default class DateTimeTool {
  constructor() {
    this.name = 'datetime_tool';
    this.description = 'Provides current date and time, and can perform date/time calculations, formatting, and timezone conversions.';

    this.commands = {
      'now': {
        description: 'Gets the current full date and time, optionally in a specific timezone. Returns a human-readable string.',
        args: [{ name: 'timezone', description: 'Optional. IANA timezone name (e.g., America/New_York).', type: 'string', optional: true }],
        examples: ['now', 'now Europe/London']
      },
      'format': {
        description: 'Formats a date/time string (or "now") using Luxon tokens. Returns the formatted string.',
        args: [
          { name: 'dateTimeStr', description: 'ISO date string or the keyword "now".', type: 'string' },
          { name: 'formatString', description: 'Luxon format string (e.g., "MMMM dd, yyyy", "h:mm a"). Quotes needed if format has spaces.', type: 'string' }
        ],
        examples: ['format now "h:mm a"', 'format 2023-10-26T10:00:00Z "MMMM dd, yyyy"']
      },
      'add': {
        description: 'Adds a specified amount of time to a date/time. Returns a human-readable string with the new date and original ISO.',
        args: [
          { name: 'dateTimeStr', description: 'ISO date string or "now".', type: 'string' },
          { name: 'amount', description: 'The amount of time to add.', type: 'number' },
          { name: 'unit', description: 'Unit of time (years, months, days, hours, etc.).', type: 'string' }
        ],
        examples: ['add now 7 days', 'add 2023-01-01 2 months']
      },
      'subtract': {
        description: 'Subtracts a specified amount of time from a date/time. Returns a human-readable string with the new date and original ISO.',
        args: [
          { name: 'dateTimeStr', description: 'ISO date string or "now".', type: 'string' },
          { name: 'amount', description: 'The amount of time to subtract.', type: 'number' },
          { name: 'unit', description: 'Unit of time (years, months, days, hours, etc.).', type: 'string' }
        ],
        examples: ['subtract now 3 hours', 'subtract 2024-12-25 1 week']
      },
      'diff': {
        description: 'Calculates the difference between two date/time values. Returns a human-readable string.',
        args: [
          { name: 'startDateTimeStr', description: 'Starting ISO date string.', type: 'string' },
          { name: 'endDateTimeStr', description: 'Ending ISO date string.', type: 'string' },
          { name: 'unit', description: 'Optional. Unit for difference (default: days).', type: 'string', optional: true }
        ],
        examples: ['diff 2023-01-01 2024-01-01 years']
      },
      'convertTimezone': { // Renamed for clarity, LLM needs to be prompted for this command name
        description: 'Converts a date/time from one timezone to another. Returns a human-readable string.',
        args: [
          { name: 'dateTimeStr', description: 'ISO date string.', type: 'string' },
          { name: 'fromZone', description: 'Source IANA timezone.', type: 'string' },
          { name: 'toZone', description: 'Target IANA timezone.', type: 'string' }
        ],
        examples: ['convertTimezone 2023-10-26T10:00:00Z America/New_York Europe/London']
      },
      'dayofweek': {
        description: 'Gets the day of the week for a date. Returns the day name.',
        args: [{ name: 'dateTimeStr', description: 'ISO date string.', type: 'string' }],
        examples: ['dayofweek 2023-10-26']
      }
    };
  }

  // Internal utility methods (mostly from original class)
  _getCurrentDateTimeISO(location) {
    try {
      let dt = location ? DateTime.now().setZone(location) : DateTime.now();
      if (!dt.isValid) throw new Error(dt.invalidReason || 'Invalid DateTime object created.');
      return dt.toISO();
    } catch (error) {
      return `Error getting current date-time: Invalid location or system error: ${location || 'default'} - ${error.message}`;
    }
  }

  _formatDateTime(dateTimeStr, format) {
    try {
      const dt = DateTime.fromISO(dateTimeStr);
      if (!dt.isValid) return `Error: Invalid date/time string for formatting: ${dateTimeStr}. Reason: ${dt.invalidReason}`;
      return dt.toFormat(format);
    } catch (error) {
      return `Error formatting date-time: ${dateTimeStr} with format ${format}. ${error.message}`;
    }
  }

  _addTimeToDateTime(dateTimeStr, amount, unit) {
    try {
      const dt = DateTime.fromISO(dateTimeStr);
      if (!dt.isValid) return `Error: Invalid base date/time for add: ${dateTimeStr}. Reason: ${dt.invalidReason}`;
      const newDt = dt.plus({ [unit]: amount });
      if (!newDt.isValid) return `Error: Calculation resulted in an invalid date after adding ${amount} ${unit}.`;
      return newDt.toISO();
    } catch (error) {
      return `Error adding time: ${amount} ${unit} to ${dateTimeStr}. ${error.message}`;
    }
  }

  _subtractTimeFromDateTime(dateTimeStr, amount, unit) {
     try {
      const dt = DateTime.fromISO(dateTimeStr);
      if (!dt.isValid) return `Error: Invalid base date/time for subtract: ${dateTimeStr}. Reason: ${dt.invalidReason}`;
      const newDt = dt.minus({ [unit]: amount });
      if (!newDt.isValid) return `Error: Calculation resulted in an invalid date after subtracting ${amount} ${unit}.`;
      return newDt.toISO();
    } catch (error)
     {
      return `Error subtracting time: ${amount} ${unit} from ${dateTimeStr}. ${error.message}`;
    }
  }

  _dateTimeDifference(startDateTimeStr, endDateTimeStr, unit = 'days') {
    try {
      const startDt = DateTime.fromISO(startDateTimeStr);
      const endDt = DateTime.fromISO(endDateTimeStr);
      if (!startDt.isValid) return `Error: Invalid start date/time for difference: ${startDateTimeStr}. Reason: ${startDt.invalidReason}`;
      if (!endDt.isValid) return `Error: Invalid end date/time for difference: ${endDateTimeStr}. Reason: ${endDt.invalidReason}`;
      
      const diff = endDt.diff(startDt, unit);
      return diff.as(unit); // Use .as(unit) for direct value
    } catch (error) {
      return `Error calculating difference: ${error.message}`;
    }
  }

  _convertToTimeZone(dateTimeStr, targetTimeZone, sourceTimeZone = null) {
      try {
          let dt = DateTime.fromISO(dateTimeStr);
          if (sourceTimeZone) {
              dt = dt.setZone(sourceTimeZone, { keepLocalTime: true });
          }
          if (!dt.isValid) return `Error: Invalid date/time for timezone conversion: ${dateTimeStr}. Reason: ${dt.invalidReason}`;
          const convertedDt = dt.setZone(targetTimeZone);
          if (!convertedDt.isValid) return `Error: Invalid target timezone or conversion failed for ${targetTimeZone}. Reason: ${convertedDt.invalidReason}`;
          return convertedDt.toISO();
      } catch (error) {
          return `Error converting timezone: ${error.message}`;
      }
  }

  // Public command methods callable by AIAgent
  async now(timezoneOpt) {
    const location = timezoneOpt; // AIAgent passes args directly
    try {
      let dt = location ? DateTime.now().setZone(location) : DateTime.now();
      if (!dt.isValid) throw new Error(dt.invalidReason || `Invalid timezone: ${location}`);
      return `Current date and time${location ? ` in ${location}` : ''}: ${dt.toFormat('MMMM d, yyyy h:mm:ss a ZZZZ')}`;
    } catch (error) {
      return `Error: Invalid location: ${location || 'system default'}. ${error.message}`;
    }
  }

  async format(...args) {
    if (args.length < 2) return "Error: Format command requires a date/time string (or 'now') and a format string.";
    let dateTimeStr = args[0];
    const formatString = args.slice(1).join(' ').replace(/^"|"$/g, ''); // Rejoin and remove surrounding quotes

    if (dateTimeStr.toLowerCase() === 'now') {
      dateTimeStr = this._getCurrentDateTimeISO();
      if (dateTimeStr.startsWith('Error:')) return dateTimeStr;
    }
    return this._formatDateTime(dateTimeStr, formatString);
  }

  async add(...args) {
    if (args.length < 3) return "Error: Add command requires a date/time string (or 'now'), amount, and unit.";
    let dateTimeStr = args[0];
    const amount = parseInt(args[1], 10);
    const unit = args[2];

    if (isNaN(amount)) return `Error: Invalid amount for add: ${args[1]}. Must be a number.`;

    const originalDateTimeStrForDisplay = dateTimeStr; // Keep "now" or original for display
    if (dateTimeStr.toLowerCase() === 'now') {
      dateTimeStr = this._getCurrentDateTimeISO();
      if (dateTimeStr.startsWith('Error:')) return dateTimeStr;
    }
    
    const resultIso = this._addTimeToDateTime(dateTimeStr, amount, unit);
    if (resultIso.startsWith('Error:')) return resultIso;
    
    const resultDt = DateTime.fromISO(resultIso);
    return `${originalDateTimeStrForDisplay} + ${amount} ${unit} = ${resultDt.toFormat('MMMM d, yyyy h:mm a')} (ISO: ${resultIso})`;
  }

  async subtract(...args) {
    if (args.length < 3) return "Error: Subtract command requires a date/time string (or 'now'), amount, and unit.";
    let dateTimeStr = args[0];
    const amount = parseInt(args[1], 10);
    const unit = args[2];

    if (isNaN(amount)) return `Error: Invalid amount for subtract: ${args[1]}. Must be a number.`;
    
    const originalDateTimeStrForDisplay = dateTimeStr;
    if (dateTimeStr.toLowerCase() === 'now') {
      dateTimeStr = this._getCurrentDateTimeISO();
      if (dateTimeStr.startsWith('Error:')) return dateTimeStr;
    }

    const resultIso = this._subtractTimeFromDateTime(dateTimeStr, amount, unit);
    if (resultIso.startsWith('Error:')) return resultIso;

    const resultDt = DateTime.fromISO(resultIso);
    return `${originalDateTimeStrForDisplay} - ${amount} ${unit} = ${resultDt.toFormat('MMMM d, yyyy h:mm a')} (ISO: ${resultIso})`;
  }

  async diff(...args) {
    if (args.length < 2) return "Error: Diff command requires two date/time strings and optionally a unit.";
    const startDateTimeStr = args[0];
    const endDateTimeStr = args[1];
    const unit = args[2] || 'days'; // Default to days

    const diffResult = this._dateTimeDifference(startDateTimeStr, endDateTimeStr, unit);
    if (typeof diffResult === 'string' && diffResult.startsWith('Error:')) return diffResult;
    return `Difference between ${startDateTimeStr} and ${endDateTimeStr}: ${diffResult} ${unit}`;
  }

  async convertTimezone(...args) { // Matches 'convertTimezone' in this.commands
    if (args.length < 3) return "Error: convertTimezone command requires dateTimeStr, fromZone, and toZone.";
    const dateTimeStr = args[0];
    const fromZone = args[1]; // AIAgent passes fromZone as first arg after command
    const toZone = args[2];   // AIAgent passes toZone as second arg

    const resultIso = this._convertToTimeZone(dateTimeStr, toZone, fromZone);
    if (resultIso.startsWith('Error:')) return resultIso;
    
    const resultDt = DateTime.fromISO(resultIso);
    return `${dateTimeStr} in ${fromZone} is ${resultDt.toFormat('MMMM d, yyyy h:mm a ZZZZ')} in ${toZone}`;
  }
  
  async dayofweek(dateTimeStr) {
    if (!dateTimeStr) return "Error: dayofweek command requires a date/time string.";
    // Handle "now" if necessary, though not explicitly in args for this command in metadata
    if (dateTimeStr.toLowerCase() === 'now') {
        dateTimeStr = this._getCurrentDateTimeISO();
        if (dateTimeStr.startsWith('Error:')) return dateTimeStr;
    }
    return this._formatDateTime(dateTimeStr, 'EEEE');
  }

  // Helper for AIAgent to get structured command documentation
  getCommandsDocumentation() {
    return Object.entries(this.commands)
      .map(([command, details]) => {
        const argsString = details.args.map(arg => `[${arg.name}${arg.optional ? '?' : ''}]`).join(' ');
        const examplesString = details.examples.map(ex => `"${ex}"`).join(', ');
        return `- ${command} ${argsString}: ${details.description} (Examples: ${examplesString})`;
      })
      .join('\n');
  }
}

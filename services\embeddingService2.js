/**
 * Ollama Embedding Service
 * 
 * Provides 768-dimensional embeddings using nomic-embed-text model
 * Perfect for Pinecone integration
 */

import { Ollama } from 'ollama';

const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

let ollama; // Will store the Ollama client instance

try {
  ollama = new Ollama({ host: OLLAMA_BASE_URL });
  console.log(`Ollama client initialized for embedding service at ${OLLAMA_BASE_URL}`);
} catch (error) {
  console.error("CRITICAL FAILURE initializing Ollama client for Embedding Service:", error);
  // ollama will remain undefined
}

/**
 * Generate embedding using Ollama's nomic-embed-text model
 * @param {string} text - The text to embed
 * @returns {Promise<number[]>} - 768-dimensional embedding vector
 */
export async function generateEmbedding(text) {
  if (!ollama) {
    console.error("generateEmbedding: Ollama client is not initialized. This indicates a startup problem.");
    throw new Error("Ollama client is not initialized. Check server startup logs for initialization errors.");
  }

  if (!text || typeof text !== 'string' || text.trim() === "") {
    console.warn("generateEmbedding called with empty or invalid text.");
    throw new Error("Cannot generate embedding for empty or invalid text.");
  }

  try {
    const result = await ollama.embeddings({
      model: 'nomic-embed-text', // 768 dimensions, perfect for Pinecone
      prompt: text
    });
    
    if (result && result.embedding && Array.isArray(result.embedding)) {
      // Verify dimension count (should be 768 for nomic-embed-text)
      if (result.embedding.length !== 768) {
        console.warn(`Expected 768 dimensions, got ${result.embedding.length}. This may cause issues with Pinecone.`);
      }
      
      return result.embedding;
    } else {
      console.error("Embedding result from Ollama is invalid or does not contain expected structure:", result);
      throw new Error("Failed to generate valid embedding structure from Ollama.");
    }
  } catch (error) {
    console.error("Error during ollama.embeddings call:", error);
    
    // Provide helpful error messages
    if (error.message && error.message.includes("model not found")) {
      console.error("The 'nomic-embed-text' model is not installed. Run: ollama pull nomic-embed-text");
    } else if (error.message && error.message.includes("connection refused")) {
      console.error("Cannot connect to Ollama server. Make sure Ollama is running on", OLLAMA_BASE_URL);
    }
    
    throw error;
  }
}

/**
 * Generate embeddings for multiple texts in batch
 * @param {string[]} texts - Array of texts to embed
 * @returns {Promise<number[][]>} - Array of 768-dimensional embedding vectors
 */
export async function generateEmbeddingsBatch(texts) {
  if (!Array.isArray(texts) || texts.length === 0) {
    throw new Error("Texts must be a non-empty array");
  }

  const embeddings = [];
  
  for (const text of texts) {
    try {
      const embedding = await generateEmbedding(text);
      embeddings.push(embedding);
    } catch (error) {
      console.error(`Failed to generate embedding for text: "${text.substring(0, 50)}..."`, error);
      // You can choose to either throw or continue with null
      embeddings.push(null);
    }
  }
  
  return embeddings;
}

/**
 * Test the embedding service
 * @returns {Promise<boolean>} - Returns true if service is working
 */
export async function testEmbeddingService() {
  try {
    const testText = "This is a test sentence for embedding.";
    const embedding = await generateEmbedding(testText);
    
    console.log(`✅ Embedding service test successful!`);
    console.log(`📏 Embedding dimensions: ${embedding.length}`);
    console.log(`🎯 Pinecone compatible: ${embedding.length === 768 ? 'Yes' : 'No'}`);
    console.log(`📊 Sample values: [${embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
    
    return true;
  } catch (error) {
    console.error("❌ Embedding service test failed:", error);
    return false;
  }
}

/**
 * Get embedding model info
 * @returns {Promise<Object>} - Model information
 */
export async function getModelInfo() {
  try {
    const modelName = 'nomic-embed-text';
    
    // Try to get model info from Ollama
    const models = await ollama.list();
    const modelInfo = models.models?.find(m => m.name.includes(modelName));
    
    return {
      name: modelName,
      dimensions: 768,
      installed: !!modelInfo,
      pineconeCompatible: true,
      description: "Nomic Embed Text - High quality 768-dimensional embeddings"
    };
  } catch (error) {
    console.error("Error getting model info:", error);
    return {
      name: 'nomic-embed-text',
      dimensions: 768,
      installed: false,
      pineconeCompatible: true,
      error: error.message
    };
  }
}
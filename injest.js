// ingest.js
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { Pinecone } from '@pinecone-database/pinecone';
import fs from 'fs/promises';
import path from 'path';

// --- Configuration ---
const PINECONE_API_KEY = process.env.PINECONE_API_KEY || 'YOUR_PINECONE_API_KEY';
const PINECONE_ENVIRONMENT = process.env.PINECONE_ENVIRONMENT || 'YOUR_PINECONE_ENVIRONMENT'; // e.g., 'gcp-starter'
const PINECONE_INDEX_NAME = process.env.PINECONE_INDEX_NAME || 'your-codebase-index'; // Make sure this index exists

const CODE_DIRECTORY = './your-codebase'; // Directory containing your .js files

const CHUNK_SIZE = 100;
const CHUNK_OVERLAP = 10;
const SNIPPET_LENGTH = 200; // Length for the 'snippet' metadata field

// --- Initialize Clients ---
const embeddings = new OpenAIEmbeddings({ apiKey: OPENAI_API_KEY });
const pc = new Pinecone({
  apiKey: PINECONE_API_KEY,
  environment: PINECONE_ENVIRONMENT,
});
const index = pc.Index(PINECONE_INDEX_NAME);

async function processFile(filePath) {
  const content = await fs.readFile(filePath, 'utf-8');
  const relativePath = path.relative(CODE_DIRECTORY, filePath);
  const fileName = path.basename(filePath);
  const entryName = fileName.replace(/\.js$/, ''); // Remove .js extension for entry_name

  console.log(`Processing file: ${relativePath}`);

  // Initialize the text splitter
  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: CHUNK_SIZE,
    chunkOverlap: CHUNK_OVERLAP,
    separators: ['\n\n', '\n', ' ', ''], // Prioritize common code delimiters
  });

  // Create documents (chunks)
  const docs = await splitter.createDocuments([content]);

  const vectorsToUpsert = [];
  for (let i = 0; i < docs.length; i++) {
    const doc = docs[i];
    const chunkText = doc.pageContent;

    // Generate a snippet for metadata
    const snippet = chunkText.substring(0, SNIPPET_LENGTH) + (chunkText.length > SNIPPET_LENGTH ? '...' : '');

    // Generate embedding for the chunk
    const embedding = await embeddings.embedQuery(chunkText);

    vectorsToUpsert.push({
      id: `${relativePath}-${i}`, // Unique ID for each chunk
      values: embedding,
      metadata: {
        entry_type: 'code_javascript',
        entry_name: entryName,
        relativePath: relativePath,
        text_chunk: chunkText, // Store the full chunk text for context
        snippet: snippet,
        chunk_index: i,
        file_name: fileName,
      },
    });
  }

  return vectorsToUpsert;
}

async function ingestKnowledgeBase() {
  try {
    const files = await fs.readdir(CODE_DIRECTORY, { withFileTypes: true });
    const jsFiles = files.filter(dirent => dirent.isFile() && dirent.name.endsWith('.js'));

    let allVectors = [];
    for (const fileDirent of jsFiles) {
      const filePath = path.join(CODE_DIRECTORY, fileDirent.name);
      const fileVectors = await processFile(filePath);
      allVectors = allVectors.concat(fileVectors);
    }

    console.log(`Prepared ${allVectors.length} vectors for upserting.`);

    // Pinecone upsert limit is 100 vectors per call (as of current docs)
    const BATCH_SIZE = 100;
    for (let i = 0; i < allVectors.length; i += BATCH_SIZE) {
      const batch = allVectors.slice(i, i + BATCH_SIZE);
      console.log(`Upserting batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(allVectors.length / BATCH_SIZE)}`);
      await index.upsert(batch);
    }

    console.log('Knowledge base ingestion complete!');

  } catch (error) {
    console.error('Error during ingestion:', error);
  }
}

// Ensure the code directory exists for the example
async function setupExampleCodeDirectory() {
    const exampleCodePath = path.join(process.cwd(), CODE_DIRECTORY);
    await fs.mkdir(exampleCodePath, { recursive: true }).catch(() => {}); // Create if not exists, ignore if exists

    // Create some dummy JS files for demonstration
    await fs.writeFile(path.join(exampleCodePath, 'Agency.js'), `
// Agency.js - Represents an advertising agency
class Agency {
    constructor(name, founder) {
        this.name = name;
        this.founder = founder;
        this.clients = [];
    }

    addClient(clientName) {
        this.clients.push(clientName);
        console.log(\`Client \${clientName} added to \${this.name}\`);
    }

    getDetails() {
        return \`Agency: \${this.name}, Founder: \${this.founder}, Clients: \${this.clients.length}\`;
    }

    // Another method example
    campaignPlanning(campaignName) {
        console.log(\`Planning campaign: \${campaignName}\`);
    }
}

export default Agency;
    `);

    await fs.writeFile(path.join(exampleCodePath, 'ClientManager.js'), `
// ClientManager.js - Manages client interactions
class ClientManager {
    constructor() {
        this.activeClients = new Map();
    }

    onboardClient(clientId, agencyName) {
        if (!this.activeClients.has(clientId)) {
            this.activeClients.set(clientId, { agency: agencyName, status: 'active' });
            console.log(\`Client \${clientId} onboarded with \${agencyName}\`);
            return true;
        }
        return false;
    }

    updateClientStatus(clientId, newStatus) {
        if (this.activeClients.has(clientId)) {
            let clientData = this.activeClients.get(clientId);
            clientData.status = newStatus;
            this.activeClients.set(clientId, clientData);
            console.log(\`Client \${clientId} status updated to \${newStatus}\`);
            return true;
        }
        return false;
    }

    getClientInfo(clientId) {
        return this.activeClients.get(clientId);
    }
}
export { ClientManager };
    `);

    await fs.writeFile(path.join(exampleCodePath, 'Utils.js'), `
// Utils.js - Utility functions
function calculateCommission(amount, rate) {
    return amount * (rate / 100);
}

const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
};

export { calculateCommission, formatCurrency };
    `);

    console.log(`Created example JS files in '${CODE_DIRECTORY}' for demonstration.`);
}


// --- Run the ingestion process ---
(async () => {
  await setupExampleCodeDirectory(); // Create dummy files for testing
  await ingestKnowledgeBase();
})();
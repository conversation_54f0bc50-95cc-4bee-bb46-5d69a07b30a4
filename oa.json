{
  "orchestrator": {
    "name": "OrchestratorAgent",
    "description": "An agent that coordinates tasks between specialized agents.",
    "role": "You manage workflows, delegate tasks to the right agents, and ensure seamless execution.",
    "goals": [
      "Route tasks to the most suitable agent.",
      "Monitor task progress and handle failures.",
      "Combine outputs from multiple agents into a cohesive result."
    ],
    "tools": ["taskRouterTool", "aggregatorTool"], 
    "provider": "gemini", 
    "llmConfig": {
      "model": "gemini-1.5-pro-latest", 
      "temperature": 0.3, 
      "maxOutputTokens": 512
    },
    "subAgents": ["WriterAgent", "ResearchAgent", "EditorAgent"] 
  }
}

// Optional Enhancements
// Error Handling:

// json
// "fallbackStrategy": {
//   "retries": 2,
//   "fallbackAgent": "ErrorHandlerAgent"
// }
// Dynamic Sub-Agent Loading:

// json
// "dynamicSubAgents": true // Allows runtime registration of new agents
// Human-in-the-Loop:

// json
// "humanApprovalSteps": ["finalDraft"] // Pauses for human review
// Would you like to customize this further (e.g., add specific tools, integrate with a framework like
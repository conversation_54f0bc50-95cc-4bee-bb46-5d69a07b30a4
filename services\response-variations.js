/**
 * Response Variations Service
 *
 * Provides a collection of alternative phrases for common AI responses
 * to add variety and personality to the agent's communication.
 */

// Collection of follow-up phrases to replace "Is there anything else I can help you with today?"
export const followUpPhrases = {
  // Direct and concise options
  direct: [
    "What else can I do for you?",
    "Anything else on your mind?",
    "What's next?",
    "Can I assist you further?",
    "Anything more I can address?",
    "What else is on your agenda?",
    "Ready for the next thing?",
    "How else may I be of service?",
    "Need anything else?",
    "What other questions do you have?",
    "What else would you like to know?",
    "Any other requests?",
    "What's your next question?",
    "Anything else to discuss?",
    "What else can I answer for you?"
  ],

  // Friendly and approachable options
  friendly: [
    "How else can I make your day easier?",
    "Anything else I can lend a hand with?",
    "Is there anything else I can do for you right now?",
    "What else can we tackle together?",
    "Anything else I can assist you with in the meantime?",
    "Just let me know if there's anything more I can do.",
    "Happy to help with anything else you need!",
    "Anything else I can do to brighten your day?",
    "I'm all ears if you have more questions!",
    "What else would you like to explore today?",
    "I'm here if you need anything else!",
    "Feel free to ask if you have other questions.",
    "What other ways can I help make your day better?",
    "I'd be happy to help with anything else you're curious about.",
    "Anything else on your mind? I'm here to chat."
  ],

  // More formal options
  formal: [
    "May I offer further assistance?",
    "Are there any other matters I can attend to?",
    "Please let me know if you require any further support.",
    "Is there anything else within my capabilities that I can do for you?",
    "Do you have any other questions or tasks for me?",
    "I remain at your service should you need anything else.",
    "Would you like assistance with any other matters?",
    "Please don't hesitate to ask if you need anything else.",
    "I'm available if you require additional assistance.",
    "Is there anything further you would like me to address?",
    "Should you have any additional inquiries, I would be pleased to assist.",
    "I'm at your disposal for any further questions.",
    "Would you care for assistance with anything else today?",
    "May I be of service in any other capacity?",
    "I'm prepared to address any additional concerns you might have."
  ],

  // Proactive and suggestive options
  proactive: [
    "What shall we explore next?",
    "Is there anything else I can clarify or elaborate on?",
    "Perhaps we could look at related topics?",
    "Would you like me to suggest a next step?",
    "Shall we proceed with anything else?",
    "Is there anything else I can proactively assist you with?",
    "Would you like to know more about this topic?",
    "I could help you with related tasks if you'd like.",
    "Any other aspects of this you'd like to explore?",
    "I'm ready to help with your next challenge.",
    "Based on our conversation, would you like to learn about...?",
    "This might also interest you. Would you like to know more?",
    "We could take this a step further if you'd like.",
    "I notice you might be interested in... Shall we explore that?",
    "There's more we could discover here. Interested?"
  ],

  // Creative and engaging options
  creative: [
    "My virtual doors are still open! Anything else I can do?",
    "Consider me at your digital beck and call. Anything else?",
    "What other wonders can I help you uncover today?",
    "Ready for the next adventure? What's on the horizon?",
    "My circuits are still buzzing with helpfulness! Anything else?",
    "Is there anything else I can conjure up for you?",
    "Let's keep the ball rolling! Anything else I can assist with?",
    "The digital genie is still here - your next wish?",
    "Your AI assistant is standing by for your next request!",
    "What other digital mountains shall we climb today?",
    "The knowledge universe is vast - where shall we journey next?",
    "My virtual notepad is ready for your next brilliant question!",
    "The conversation doesn't have to end here - what else intrigues you?",
    "I've got more answers up my digital sleeve if you have more questions!",
    "Your curiosity is my command - what else would you like to discover?"
  ],

  // Context-aware templates (to be filled in with context)
  contextAware: [
    "Now that we've [completed previous task], is there anything else I can help you with?",
    "Following up on [previous topic], is there anything else related I can assist with?",
    "Before we wrap up, is there anything else you'd like to discuss?",
    "With [previous task] taken care of, what else can I help with?",
    "Since we've addressed [previous issue], is there anything else on your mind?",
    "Having resolved [previous problem], can I help with anything else?",
    "After exploring [previous topic], would you like to dive into anything else?",
    "Now that I've helped with [previous task], what's next on your list?",
    "With [previous question] answered, what else are you curious about?",
    "Having completed [previous action], where else can I be of service?",
    "We've made good progress with [previous topic]. Anything else to tackle?",
    "That covers [previous topic]. Would you like to explore a related area?",
    "I hope that helps with [previous issue]. Is there anything else you're wondering about?",
    "Now that you know about [previous topic], perhaps you'd like to learn about something related?",
    "I've addressed [previous question] - is there a follow-up question I can help with?"
  ],

  // Conversational and natural-sounding options
  conversational: [
    "So, what's next on your mind?",
    "Anything else you're curious about?",
    "What else are you thinking about today?",
    "Got any other questions?",
    "What else would you like to chat about?",
    "Anything else you'd like to know?",
    "What other thoughts do you have?",
    "Where would you like to take the conversation next?",
    "I'm curious - what else is on your mind?",
    "What other topics interest you right now?",
    "Anything else you're wondering about?",
    "What else would be helpful for you today?",
    "Any other thoughts or questions?",
    "What else would you like to talk through?",
    "Where should we go from here?"
  ]
};

// Style weights for different contexts
const styleWeights = {
  default: {
    direct: 0.15,
    friendly: 0.25,
    formal: 0.1,
    proactive: 0.15,
    creative: 0.1,
    contextAware: 0.1,
    conversational: 0.15
  },
  professional: {
    direct: 0.25,
    friendly: 0.1,
    formal: 0.35,
    proactive: 0.1,
    creative: 0.0,
    contextAware: 0.1,
    conversational: 0.1
  },
  casual: {
    direct: 0.1,
    friendly: 0.3,
    formal: 0.0,
    proactive: 0.15,
    creative: 0.15,
    contextAware: 0.1,
    conversational: 0.2
  },
  varjis_llm: {
    direct: 0.15,
    friendly: 0.0,
    formal: 0.25,
    proactive: 0.15,
    creative: 0.2,
    contextAware: 0.1,
    conversational: 0.15
  }
};

/**
 * Select a style based on weights
 * @param {string} personaId - The current persona ID
 * @param {object} context - Context information
 * @returns {string} - The selected style
 */
function selectWeightedStyle(personaId, context = {}) {
  // Get the appropriate weight set
  const weights = styleWeights[personaId] || styleWeights.default;

  // Adjust weights based on context
  const adjustedWeights = { ...weights };

  // If we have context, increase the weight of contextAware
  if (context && (context.previousTopic || context.previousTask)) {
    adjustedWeights.contextAware = Math.min(adjustedWeights.contextAware * 2, 0.3);

    // Normalize other weights
    const totalOtherWeight = 1 - adjustedWeights.contextAware;
    const currentOtherWeight = Object.entries(adjustedWeights)
      .filter(([key]) => key !== 'contextAware')
      .reduce((sum, [, weight]) => sum + weight, 0);

    const factor = totalOtherWeight / currentOtherWeight;

    for (const key in adjustedWeights) {
      if (key !== 'contextAware') {
        adjustedWeights[key] *= factor;
      }
    }
  }

  // Select a style based on weights
  const random = Math.random();
  let cumulativeWeight = 0;

  for (const [style, weight] of Object.entries(adjustedWeights)) {
    cumulativeWeight += weight;
    if (random < cumulativeWeight) {
      return style;
    }
  }

  // Fallback to friendly
  return 'friendly';
}

/**
 * Get a random follow-up phrase
 * @param {string} style - The style of phrase to get (direct, friendly, formal, proactive, creative, or random)
 * @param {object} context - Optional context for context-aware phrases
 * @returns {string} - A random follow-up phrase
 */
export function getRandomFollowUpPhrase(style = 'random', context = null) {
  // If style is random, use weighted selection
  if (style === 'random') {
    style = selectWeightedStyle(context?.personaId || 'default', context);
  }

  // Get phrases for the selected style
  const phrases = followUpPhrases[style] || followUpPhrases.friendly;

  // Pick a random phrase
  const phrase = phrases[Math.floor(Math.random() * phrases.length)];

  // If using context-aware phrases and context is provided, fill in the template
  if (style === 'contextAware') {
    // Ensure context is an object to avoid errors
    const safeContext = context || {};

    // Create a function to safely replace placeholders with fallbacks
    const safeReplace = (text, placeholder, contextKey, fallback) => {
      // Check if the context value exists and is not empty
      const value = safeContext[contextKey];
      const replacement = (value && typeof value === 'string' && value.trim() !== '')
        ? value.trim()
        : fallback;
      return text.replace(placeholder, replacement);
    };

    // Apply all replacements with safe fallbacks
    let result = phrase;
    result = safeReplace(result, '[completed previous task]', 'previousTask', 'completing that');
    result = safeReplace(result, '[previous topic]', 'previousTopic', 'our discussion');
    result = safeReplace(result, '[previous issue]', 'previousIssue', 'that issue');
    result = safeReplace(result, '[previous problem]', 'previousProblem', 'that problem');
    result = safeReplace(result, '[previous question]', 'previousQuestion', 'your question');
    result = safeReplace(result, '[previous action]', 'previousAction', 'that');

    return result;
  }

  return phrase;
}

/**
 * Add new follow-up phrases to the collection
 * @param {string} style - The style category to add phrases to
 * @param {string[]} phrases - Array of phrases to add
 * @returns {boolean} - Success status
 */
export function addFollowUpPhrases(style, phrases) {
  if (!style || !Array.isArray(phrases) || phrases.length === 0) {
    console.error('Invalid parameters for addFollowUpPhrases');
    return false;
  }

  // Create the category if it doesn't exist
  if (!followUpPhrases[style]) {
    followUpPhrases[style] = [];
  }

  // Add the new phrases
  followUpPhrases[style].push(...phrases);
  console.log(`Added ${phrases.length} phrases to the '${style}' category`);
  return true;
}

/**
 * Load follow-up phrases from an external source
 * @param {object} phrasesData - Object containing phrases categorized by style
 * @returns {boolean} - Success status
 */
export function loadFollowUpPhrases(phrasesData) {
  if (!phrasesData || typeof phrasesData !== 'object') {
    console.error('Invalid phrases data format');
    return false;
  }

  let totalAdded = 0;

  // Add phrases from each category
  for (const [style, phrases] of Object.entries(phrasesData)) {
    if (Array.isArray(phrases)) {
      if (!followUpPhrases[style]) {
        followUpPhrases[style] = [];
      }

      // Filter out duplicates
      const newPhrases = phrases.filter(phrase =>
        !followUpPhrases[style].includes(phrase)
      );

      followUpPhrases[style].push(...newPhrases);
      totalAdded += newPhrases.length;
    }
  }

  console.log(`Loaded ${totalAdded} new follow-up phrases`);
  return totalAdded > 0;
}

/**
 * Get all follow-up phrases
 * @returns {object} - All follow-up phrases categorized by style
 */
export function getAllFollowUpPhrases() {
  return followUpPhrases;
}

export default {
  followUpPhrases,
  getRandomFollowUpPhrase,
  addFollowUpPhrases,
  loadFollowUpPhrases,
  getAllFollowUpPhrases
};

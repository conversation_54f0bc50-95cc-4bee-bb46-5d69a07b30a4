import 'dotenv/config'; // Load .env file for API keys

import {
    addEntry,
    getEntry,
    listEntries,
    searchEntries,
    ensureBaseDirectories
} from './KnowledgeBaseManager.js';

async function runKBTests() {
    try {
        console.log("Starting Knowledge Base Manager tests...");
        // ensureBaseDirectories will also initialize Pinecone
        // No need to call it separately if other functions that call it are used first,
        // but calling it explicitly here ensures Pinecone is ready before any specific add/get/search.
        await ensureBaseDirectories(); 

        // Test: Add a configuration
        console.log("\n[Test] Adding configuration 'test_server.json'...");
        await addEntry('configuration', 'test_server.json', JSON.stringify({ host: 'test_host', port: 1234 }, null, 2));

        // Test: Add a note
        console.log("\n[Test] Adding note 'test_meeting_notes.md'...");
        await addEntry('note', 'test_meeting_notes.md', '# Test Meeting\n- Discussed test project\n- Action: Test follow up');

        // Test: Add a snippet with metadata
        console.log("\n[Test] Adding snippet 'test_code_snippet.js'...");
        const snippetContent = "console.log('This is a test code snippet for KnowledgeBaseManager');";
        const snippetMetadata = { description: "A test snippet for KB Manager", tags: ["kb-test", "example-code"] };
        await addEntry('snippet', 'test_code_snippet.js', snippetContent, snippetMetadata);

        // Give Pinecone a moment to index if operations are very fast
        console.log("\nWaiting a few seconds for Pinecone to potentially index...");
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 seconds wait

        // Test: List all entries
        console.log('\n[Test] Listing all entries (from filesystem):');
        const allEntries = await listEntries();
        console.log(allEntries);

        // Test: List only snippets
        console.log('\n[Test] Listing snippets (from filesystem):');
        const snippets = await listEntries('snippet');
        console.log(snippets);

        // Test: Get a specific entry (configuration)
        console.log('\n[Test] Getting configuration "test_server.json" (from filesystem):');
        const serverConfig = await getEntry('configuration', 'test_server.json');
        console.log('Content:', serverConfig.content);

        // Test: Get a specific entry (snippet)
        console.log('\n[Test] Getting snippet "test_code_snippet.js" (from filesystem):');
        const snippetEntry = await getEntry('snippet', 'test_code_snippet.js');
        console.log('Content:', snippetEntry.content);
        console.log('Metadata:', snippetEntry.metadata);

        // Test: Search for "test_host" in configurations (will use Pinecone if available)
        console.log('\n[Test] Searching for "test_host" in configurations (Pinecone/fallback):');
        let searchResults = await searchEntries('test_host', 'configuration');
        console.log(searchResults);

        // Test: Search for "test project" in notes (will use Pinecone if available)
        console.log('\n[Test] Searching for "test project" in notes (Pinecone/fallback):');
        searchResults = await searchEntries('test project', 'note');
        console.log(searchResults);
        
        // Test: Search for "snippet" across all types (will use Pinecone if available)
        console.log('\n[Test] Searching for "snippet" (all types - Pinecone/fallback):');
        searchResults = await searchEntries('snippet'); 
        console.log(searchResults);
        
        // Test: Search for a specific snippet content
        console.log('\n[Test] Searching for "test code snippet for KnowledgeBaseManager" (all types - Pinecone/fallback):');
        searchResults = await searchEntries('test code snippet for KnowledgeBaseManager');
        console.log(searchResults);


        console.log("\nKnowledge Base Manager tests completed.");

    } catch (error) {
        console.error('Error during Knowledge Base Manager tests:', error);
    }
}

runKBTests();

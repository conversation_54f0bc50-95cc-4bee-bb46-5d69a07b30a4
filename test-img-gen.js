import dotenv from 'dotenv';
import { imageTool } from './services/tools/imgGenTool.js';
import path from 'path'; // For potential path operations if needed, though console.log is primary

// Load environment variables from .env file
dotenv.config();

async function testImageGeneration() {
  console.log("Attempting to generate an image using imgGenTool...");

  const testPrompt = "a futuristic cityscape with flying cars";
  const params = {
    action: "generate",
    query: testPrompt,
    options: {
      aspectRatio: "16:9" 
    }
  };
  const paramsStr = JSON.stringify(params);

  try {
    const result = await imageTool(paramsStr);
    console.log("\n--- imgGenTool Result ---");
    console.log(result);

    if (typeof result === 'string' && result.startsWith('Generated')) {
      const lines = result.split('\n');
      if (lines.length > 1) {
        console.log("\n--- Public URLs (if server is running and configured) ---");
        for (let i = 1; i < lines.length; i++) {
          const filePath = lines[i].trim();
          if (filePath) {
            const baseName = path.basename(filePath.replace(/\\/g, '/'));
            // Assuming your app serves 'generated-images' from the project root
            console.log(`http://localhost:3081/generated-images/${baseName}`); 
          }
        }
      }
    } else if (typeof result === 'string' && result.toLowerCase().includes('error')) {
      console.error("\nImage generation failed with an error from the tool.");
    }

  } catch (error) {
    console.error("\n--- Error during testImageGeneration ---");
    console.error(error);
  }
}

testImageGeneration();

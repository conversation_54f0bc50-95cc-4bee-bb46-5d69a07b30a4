const { Ollama } = require('ollama');

class OllamaProvider {
  constructor(modelName = 'gemma3:1b', baseUrl = 'http://localhost:11434', defaultOptions = {}) {
    if (!modelName) {
      throw new Error('Model name is required');
    }
    if (!baseUrl) {
      throw new Error('Base URL is required');
    }

    this.modelName = modelName;
    this.ollama = new Ollama({ host: baseUrl });
    this.defaultOptions = {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      num_predict: 2048,
      ...defaultOptions
    };
  }

  _formatMessages(prompt) {
    // Handle string input
    if (typeof prompt === 'string') {
      return [{ role: 'user', content: prompt }];
    }

    // Handle simple text input
    if (prompt && prompt.text) {
      return [{ role: 'user', content: prompt.text }];
    }

    const messages = [];

    // Add history if present
    if (prompt && prompt.history && Array.isArray(prompt.history)) {
      const historyMessages = prompt.history.map(item => {
        let content = item.content;
        
        // Handle parts array format
        if (item.parts && Array.isArray(item.parts)) {
          content = item.parts.map(p => p.text || '').join('\n');
        }
        
        return {
          role: item.role || 'user',
          content: content || ''
        };
      });
      
      messages.push(...historyMessages);
    }

    // Add current contents
    if (prompt && prompt.contents) {
      const contents = Array.isArray(prompt.contents) ? prompt.contents : [prompt.contents];
      
      contents.forEach(item => {
        let content = item;
        
        // Handle object with content property
        if (typeof item === 'object' && item !== null) {
          if (item.content) {
            content = item.content;
          } else if (item.parts && Array.isArray(item.parts)) {
            content = item.parts.map(p => p.text || '').join('\n');
          }
        }
        
        messages.push({
          role: (typeof item === 'object' && item.role) || 'user',
          content: String(content || '')
        });
      });
    }

    // Return messages or default fallback
    return messages.length > 0 ? messages : [{ role: 'user', content: 'Hello' }];
  }

  async generateContent(prompt, generationConfig = {}) {
    try {
      return await this.retryWithBackoff(async () => {
        const response = await this.ollama.chat({
          model: this.modelName,
          messages: this._formatMessages(prompt),
          options: {
            ...this.defaultOptions,
            temperature: generationConfig.temperature !== undefined 
              ? generationConfig.temperature 
              : this.defaultOptions.temperature,
            top_p: generationConfig.topP !== undefined 
              ? generationConfig.topP 
              : this.defaultOptions.top_p,
            top_k: generationConfig.topK !== undefined 
              ? generationConfig.topK 
              : this.defaultOptions.top_k,
            num_predict: generationConfig.maxOutputTokens !== undefined 
              ? generationConfig.maxOutputTokens 
              : this.defaultOptions.num_predict,
          },
        });
        
        return (response && response.message && response.message.content) || '';
      });
    } catch (error) {
      console.error('OllamaProvider Error:', error);
      throw new Error(`Ollama API error: ${error.message}`);
    }
  }

  async generateContentStream(prompt, generationConfig = {}, onChunk) {
    try {
      const stream = await this.ollama.chat({
        model: this.modelName,
        messages: this._formatMessages(prompt),
        options: {
          ...this.defaultOptions,
          temperature: generationConfig.temperature !== undefined 
            ? generationConfig.temperature 
            : this.defaultOptions.temperature,
          top_p: generationConfig.topP !== undefined 
            ? generationConfig.topP 
            : this.defaultOptions.top_p,
          top_k: generationConfig.topK !== undefined 
            ? generationConfig.topK 
            : this.defaultOptions.top_k,
          num_predict: generationConfig.maxOutputTokens !== undefined 
            ? generationConfig.maxOutputTokens 
            : this.defaultOptions.num_predict,
        },
        stream: true,
      });

      let fullResponse = '';
      
      for await (const chunk of stream) {
        const chunkText = (chunk && chunk.message && chunk.message.content) || '';
        fullResponse += chunkText;
        
        if (typeof onChunk === 'function') {
          onChunk(chunkText);
        }
      }
      
      return fullResponse;
    } catch (error) {
      console.error('OllamaProvider Stream Error:', error);
      throw new Error(`Ollama streaming error: ${error.message}`);
    }
  }

  async retryWithBackoff(operation, maxRetries = 3, initialDelay = 1000) {
    if (typeof operation !== 'function') {
      throw new Error('Operation must be a function');
    }
    
    let retries = 0;
    let delay = initialDelay;
    
    while (retries < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        const errorMessage = error.message || '';
        const isRetryable = 
          errorMessage.includes('ECONNREFUSED') ||
          errorMessage.includes('503') ||
          errorMessage.includes('429') ||
          errorMessage.includes('UNAVAILABLE') ||
          errorMessage.includes('overloaded');
        
        if (!isRetryable || retries >= maxRetries - 1) {
          throw error;
        }
        
        retries++;
        console.log(`Retry ${retries}/${maxRetries} after ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * 2, 30000); // Cap at 30 seconds
      }
    }
  }
}

module.exports = { OllamaProvider };
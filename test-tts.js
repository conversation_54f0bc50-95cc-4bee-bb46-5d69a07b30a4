import dotenv from 'dotenv';
import { synthesizeSpeech, synthesizeSpeechStream } from './services/textToSpeech-simplified.js';
import fs from 'fs';

// Load environment variables
dotenv.config();

async function testTTS() {
  console.log("Testing TTS functionality...");
  
  try {
    // Test 1: Basic TTS synthesis
    console.log("\n--- Test 1: Basic TTS synthesis ---");
    const testText = "Hello, this is a test of the text-to-speech functionality.";
    console.log(`Testing with text: "${testText}"`);
    
    const audioBuffer = await synthesizeSpeech(testText);
    
    if (audioBuffer) {
      console.log(`✅ TTS synthesis successful! Generated ${audioBuffer.length} bytes of audio`);
      
      // Save the audio to a file for verification
      const outputPath = './test-output.mp3';
      fs.writeFileSync(outputPath, audioBuffer);
      console.log(`Audio saved to: ${outputPath}`);
    } else {
      console.log("❌ TTS synthesis returned null");
    }
    
    // Test 2: TTS streaming
    console.log("\n--- Test 2: TTS streaming ---");
    const streamText = "This is a test of the streaming text-to-speech functionality.";
    console.log(`Testing stream with text: "${streamText}"`);
    
    const audioStream = await synthesizeSpeechStream(streamText);
    
    if (audioStream) {
      console.log("✅ TTS streaming successful! Stream created");
      
      // Test reading from the stream
      let streamData = Buffer.alloc(0);
      audioStream.on('data', (chunk) => {
        streamData = Buffer.concat([streamData, chunk]);
      });
      
      audioStream.on('end', () => {
        console.log(`Stream completed with ${streamData.length} bytes`);
        
        // Save the streamed audio to a file for verification
        const streamOutputPath = './test-stream-output.mp3';
        fs.writeFileSync(streamOutputPath, streamData);
        console.log(`Streamed audio saved to: ${streamOutputPath}`);
      });
      
      audioStream.on('error', (error) => {
        console.error("❌ Stream error:", error);
      });
    } else {
      console.log("❌ TTS streaming returned null");
    }
    
    // Test 3: Long text chunking
    console.log("\n--- Test 3: Long text chunking ---");
    const longText = "This is a very long text that should be split into multiple chunks for processing. ".repeat(100);
    console.log(`Testing with long text (${longText.length} characters)`);
    
    const longAudioBuffer = await synthesizeSpeech(longText);
    
    if (longAudioBuffer) {
      console.log(`✅ Long text TTS synthesis successful! Generated ${longAudioBuffer.length} bytes of audio`);
      
      // Save the long audio to a file for verification
      const longOutputPath = './test-long-output.mp3';
      fs.writeFileSync(longOutputPath, longAudioBuffer);
      console.log(`Long audio saved to: ${longOutputPath}`);
    } else {
      console.log("❌ Long text TTS synthesis returned null");
    }
    
    console.log("\n🎉 TTS testing completed!");
    
  } catch (error) {
    console.error("❌ TTS test failed:", error);
    console.error("Error details:", error.message);
    
    // Check if it's a credentials issue
    if (error.message.includes('TTS client not available')) {
      console.log("\n💡 This appears to be a Google Cloud credentials issue.");
      console.log("Make sure you have:");
      console.log("1. Set up Google Cloud Text-to-Speech API");
      console.log("2. Created a service account with TTS permissions");
      console.log("3. Downloaded the credentials JSON file");
      console.log("4. Set GOOGLE_APPLICATION_CREDENTIALS environment variable");
      console.log("   or placed credentials.json in the project root");
    }
  }
}

// Run the test
testTTS();

/* Time Display Styles */

.time-display-toggle {
  position: relative;
  display: inline-block;
  margin-left: 8px;
  cursor: pointer;
  color: #aaa;
  transition: color 0.2s;
}

.time-display-toggle:hover {
  color: #fff;
}

.time-display-toggle i {
  font-size: 14px;
}

.time-display-settings {
  position: absolute;
  top: 100%;
  right: 0;
  width: 250px;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  display: none;
}

.time-display-settings.active {
  display: block;
}

.time-display-settings h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #fff;
  border-bottom: 1px solid #333;
  padding-bottom: 8px;
}

.time-display-option {
  margin-bottom: 10px;
}

.time-display-option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #ddd;
}

.time-display-option input[type="radio"],
.time-display-option input[type="checkbox"] {
  margin-right: 8px;
}

.time-display-option:last-child {
  margin-bottom: 0;
}

.time-display-option .description {
  font-size: 12px;
  color: #888;
  margin-left: 20px;
  margin-top: 2px;
}

/* Time markers in messages */
.message-time {
  font-size: 12px;
  color: #888;
  margin-left: 8px;
  white-space: nowrap;
}

.message-time.absolute {
  color: #888;
}

.message-time.relative {
  color: #aaa;
}

/* Day and time period headers */
.time-header {
  text-align: center;
  margin: 16px 0;
  position: relative;
}

.time-header::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 1px;
  background-color: #333;
  z-index: -1;
}

.time-header-text {
  display: inline-block;
  background-color: #0f0f0f;
  padding: 0 12px;
  font-size: 14px;
  color: #888;
  border-radius: 12px;
}

.day-header .time-header-text {
  font-weight: bold;
  color: #aaa;
  padding: 4px 16px;
  background-color: #1a1a1a;
}

.period-header .time-header-text {
  font-style: italic;
  color: #777;
}

/* Hide time elements when disabled */
.time-hidden .message-time,
.time-hidden .time-header {
  display: none;
}

/* Animations */
.time-header, .message-time {
  transition: opacity 0.3s ease;
}

.time-fade-in {
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.time-fade-out {
  opacity: 1;
  animation: fadeOut 0.3s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

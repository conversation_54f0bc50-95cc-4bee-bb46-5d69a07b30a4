{"name": "vari", "version": "1.0.0", "main": "app-simplified.js", "type": "module", "scripts": {"start": "node app-simplified", "dev": "nodemon app-simplified"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@google-cloud/speech": "^7.1.0", "@google-cloud/text-to-speech": "^6.1.0", "@google/genai": "^0.14.1", "@pinecone-database/pinecone": "^6.0.1", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "googleapis": "^148.0.0", "luxon": "^3.6.1", "mathjs": "^14.4.0", "mongoose": "^8.15.0", "multer": "^1.4.5-lts.2", "puppeteer": "^24.8.2"}}
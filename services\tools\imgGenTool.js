import { GoogleGenAI, createUserContent, createPartFromUri } from "@google/genai";
import fs from 'fs/promises';
import path from 'path';

// Configure with your API key
const API_KEY = process.env.GEMINI_API_KEY;

/**
 * Image tool for agent framework
 * Allows the agent to analyze and generate images using Google's Gemini API
 * @param {string} paramsStr - JSON string or command for the image tool
 * @returns {Promise<string>} - Result of the image operation
 */
export async function imageTool(paramsStr) {
  try {
    // Parse parameters
    let action, query, options = {};

    if (typeof paramsStr === 'string') {
      // Try to parse as JSON first
      try {
        const params = JSON.parse(paramsStr);
        action = params.action;
        query = params.query;
        options = params.options || {};
      } catch (e) {
        // Parse command format: "analyze path/to/image.jpg" or "generate prompt"
        const match = paramsStr.match(/^(analyze|generate|detect|segment|compare|style)\s+"(.+)"$/);
        if (match) {
          action = match[1];
          query = match[2];
        } else {
          return "Invalid format. Use: analyze|detect|segment|compare|generate|style \"parameters\"";
        }
      }
    } else {
      return "Invalid input. Expected string parameter.";
    }

    // Initialize Google Gemini AI
    const ai = new GoogleGenAI({ apiKey: API_KEY });

    switch (action) {
      case "analyze":
        return await analyzeImage(ai, query);
      case "generate":
        return await generateImage(ai, query, options);
      case "detect":
        return await detectObjects(ai, query);
      case "segment":
        return await segmentImage(ai, query);
      case "compare":
        return await compareImages(ai, query, options);
      case "style":
        return styleGuide(query);
      default:
        return `Unknown action: ${action}. Available actions: analyze, generate, detect, segment, compare, style`;
    }
  } catch (error) {
    console.error("Error in image tool:", error);
    return `Error processing image request: ${error.message}`;
  }
}

/**
 * Analyze an image using Gemini
 * @param {GoogleGenAI} ai - Initialized GoogleGenAI instance
 * @param {string} imagePath - Path to the image file
 * @returns {Promise<string>} - Analysis result
 */
async function analyzeImage(ai, imagePath) {
  try {
    // Validate file exists
    await fs.access(imagePath);

    // Get file extension and determine MIME type
    const ext = path.extname(imagePath).toLowerCase();
    let mimeType;

    if (['.jpg', '.jpeg'].includes(ext)) mimeType = 'image/jpeg';
    else if (ext === '.png') mimeType = 'image/png';
    else if (ext === '.webp') mimeType = 'image/webp';
    else if (ext === '.heic') mimeType = 'image/heic';
    else if (ext === '.heif') mimeType = 'image/heif';
    else if (ext === '.gif') mimeType = 'image/gif'; // Note: GIF might not be fully supported by Gemini
    else return `Unsupported image format: ${ext}. Supported formats are: JPG, PNG, WEBP, HEIC, HEIF`;

    // Get image dimensions and token usage info
    const imageBuffer = await fs.readFile(imagePath);
    const imageInfo = await getImageDimensions(imageBuffer);

    // Add token usage warning for large images
    let tokenWarning = "";
    if (imageInfo.isLarge) {
      tokenWarning = `\n\nNote: This image is ${imageInfo.width}x${imageInfo.height} pixels and will use approximately ${imageInfo.tokenEstimate} tokens (${imageInfo.tileCount} tiles) when processed by Gemini.`;
    }

    // Upload file to Gemini
    const file = await ai.files.upload({
      file: imagePath,
      config: { mimeType }
    });

    // Analyze the image
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: createUserContent([
        createPartFromUri(file.uri, file.mimeType),
        "Describe this image in detail."
      ])
    });

    return response.text + tokenWarning;
  } catch (error) {
    console.error("Error analyzing image:", error);
    return `Error analyzing image: ${error.message}`;
  }
}

/**
 * Generate an image using Imagen
 * @param {GoogleGenAI} ai - Initialized GoogleGenAI instance
 * @param {string} prompt - Text description for image generation
 * @param {Object} options - Additional options for image generation
 * @returns {Promise<string>} - Path to generated image or error message
 */
async function generateImage(ai, prompt, options = {}) {
  try {
    // Configure generation options
    const config = {
      numberOfImages: options.numberOfImages || 1,
    };

    // Add aspect ratio if specified
    if (options.aspectRatio) {
      const validRatios = ["1:1", "3:4", "4:3", "9:16", "16:9"];
      if (validRatios.includes(options.aspectRatio)) {
        config.aspectRatio = options.aspectRatio;
      } else {
        return `Invalid aspect ratio. Supported values are: ${validRatios.join(", ")}`;
      }
    }

    // Add person generation setting if specified
    if (options.personGeneration) {
      const validSettings = ["DONT_ALLOW", "ALLOW_ADULT"];
      if (validSettings.includes(options.personGeneration)) {
        config.personGeneration = options.personGeneration;
      } else {
        return `Invalid person generation setting. Supported values are: ${validSettings.join(", ")}`;
      }
    }

    // Enhance prompt with quality modifiers if requested
    if (options.enhanceQuality) {
      prompt = enhancePromptWithQualityModifiers(prompt, options.style);
    }

    // Generate image using Imagen
    const response = await ai.models.generateImages({
      model: 'imagen-3.0-generate-002',
      prompt: prompt,
      config: config
    });

    if (response?.generatedImages?.[0]?.image?.imageBytes) {
      // Save image to disk
      const outputDir = path.join(process.cwd(), 'generated-images');

      // Create directory if it doesn't exist
      await fs.mkdir(outputDir, { recursive: true });

      // Save all generated images
      const savedPaths = [];

      for (let i = 0; i < response.generatedImages.length; i++) {
        const imageBytes = response.generatedImages[i].image.imageBytes;

        // Generate unique filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `image-${timestamp}-${i+1}.png`;
        const outputPath = path.join(outputDir, filename);

        // Write image to file
        await fs.writeFile(outputPath, Buffer.from(imageBytes, 'base64'));
        savedPaths.push(outputPath);
      }

      return `Generated ${savedPaths.length} image(s) successfully:\n${savedPaths.join('\n')}`;
    } else {
      return "Failed to generate image: No image data received";
    }
  } catch (error) {
    console.error("Error generating image:", error);
    return `Error generating image: ${error.message}`;
  }
}

/**
 * Detect objects in an image and return bounding boxes
 * @param {GoogleGenAI} ai - Initialized GoogleGenAI instance
 * @param {string} imagePath - Path to the image file
 * @returns {Promise<string>} - JSON string with detected objects and bounding boxes
 */
async function detectObjects(ai, imagePath) {
  try {
    // Validate file exists
    await fs.access(imagePath);

    // Get file extension and determine MIME type
    const ext = path.extname(imagePath).toLowerCase();
    let mimeType;

    if (['.jpg', '.jpeg'].includes(ext)) mimeType = 'image/jpeg';
    else if (ext === '.png') mimeType = 'image/png';
    else if (ext === '.webp') mimeType = 'image/webp';
    else if (ext === '.heic') mimeType = 'image/heic';
    else if (ext === '.heif') mimeType = 'image/heif';
    else if (ext === '.gif') mimeType = 'image/gif'; // Note: GIF might not be fully supported by Gemini
    else return `Unsupported image format: ${ext}. Supported formats are: JPG, PNG, WEBP, HEIC, HEIF`;

    // Upload file to Gemini
    const file = await ai.files.upload({
      file: imagePath,
      config: { mimeType }
    });

    // Detect objects with bounding boxes
    const prompt = "Detect all prominent items in the image. The box_2d should be [ymin, xmin, ymax, xmax] normalized to 0-1000.";
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: createUserContent([
        createPartFromUri(file.uri, file.mimeType),
        prompt
      ])
    });

    // Get image dimensions for coordinate conversion
    const imageBuffer = await fs.readFile(imagePath);
    const imageInfo = await getImageDimensions(imageBuffer);

    // Create a formatted result object
    const result = {
      detectionResult: response.text,
      imageWidth: imageInfo.width,
      imageHeight: imageInfo.height,
      tokenUsage: {
        estimatedTokens: imageInfo.tokenEstimate,
        tileCount: imageInfo.tileCount,
        isLargeImage: imageInfo.isLarge
      },
      note: "To convert normalized coordinates to pixels: divide by 1000, then multiply x by width and y by height"
    };

    // Return as a formatted string for better display in the agent
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error detecting objects:", error);
    return `Error detecting objects: ${error.message}`;
  }
}

/**
 * Generate segmentation masks for objects in an image
 * @param {GoogleGenAI} ai - Initialized GoogleGenAI instance
 * @param {string} imagePath - Path to the image file
 * @returns {Promise<string>} - JSON string with segmentation masks, bounding boxes, and labels
 */
async function segmentImage(ai, imagePath) {
  try {
    // Validate file exists
    await fs.access(imagePath);

    // Get file extension and determine MIME type
    const ext = path.extname(imagePath).toLowerCase();
    let mimeType;

    if (['.jpg', '.jpeg'].includes(ext)) mimeType = 'image/jpeg';
    else if (ext === '.png') mimeType = 'image/png';
    else if (ext === '.webp') mimeType = 'image/webp';
    else if (ext === '.heic') mimeType = 'image/heic';
    else if (ext === '.heif') mimeType = 'image/heif';
    else if (ext === '.gif') mimeType = 'image/gif'; // Note: GIF might not be fully supported by Gemini
    else return `Unsupported image format: ${ext}. Supported formats are: JPG, PNG, WEBP, HEIC, HEIF`;

    // Upload file to Gemini
    const file = await ai.files.upload({
      file: imagePath,
      config: { mimeType }
    });

    // Request segmentation masks
    const prompt = `
      Give the segmentation masks for all prominent items in the image.
      Output a JSON list of segmentation masks where each entry contains the 2D
      bounding box in the key "box_2d", the segmentation mask in key "mask", and
      the text label in the key "label". Use descriptive labels.
    `;

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: createUserContent([
        createPartFromUri(file.uri, file.mimeType),
        prompt
      ])
    });

    // Try to parse the response as JSON to extract masks
    let parsedResponse;
    try {
      // Extract JSON from the response text (it might be wrapped in markdown code blocks)
      const jsonMatch = response.text.match(/```json\n([\s\S]*?)\n```/) ||
                        response.text.match(/```\n([\s\S]*?)\n```/) ||
                        [null, response.text];

      const jsonText = jsonMatch[1];
      parsedResponse = JSON.parse(jsonText);

      // Check if we have masks and save them to disk
      if (Array.isArray(parsedResponse)) {
        const outputDir = path.join(process.cwd(), 'segmentation-masks');
        await fs.mkdir(outputDir, { recursive: true });

        // Process each mask
        for (let i = 0; i < parsedResponse.length; i++) {
          const item = parsedResponse[i];
          if (item.mask && item.label) {
            // Generate a filename based on the original image and object label
            const baseName = path.basename(imagePath, path.extname(imagePath));
            const maskFileName = `${baseName}-${item.label.replace(/\s+/g, '_')}-mask-${i+1}.png`;
            const maskPath = path.join(outputDir, maskFileName);

            // If the mask is base64 encoded, save it
            if (typeof item.mask === 'string') {
              try {
                const maskBuffer = Buffer.from(item.mask, 'base64');
                await fs.writeFile(maskPath, maskBuffer);

                // Add the saved path to the item
                item.maskPath = maskPath;
              } catch (e) {
                console.warn(`Could not save mask for ${item.label}: ${e.message}`);
              }
            }
          }
        }
      }
    } catch (e) {
      console.warn("Could not parse segmentation masks as JSON:", e.message);
      // Continue with the text response
    }

    // Get image dimensions and token usage info
    const imageBuffer = await fs.readFile(imagePath);
    const imageInfo = await getImageDimensions(imageBuffer);

    // Create a formatted result object
    const result = {
      segmentationResult: response.text,
      parsedMasks: parsedResponse || null,
      imageWidth: imageInfo.width,
      imageHeight: imageInfo.height,
      tokenUsage: {
        estimatedTokens: imageInfo.tokenEstimate,
        tileCount: imageInfo.tileCount,
        isLargeImage: imageInfo.isLarge
      },
      note: "Segmentation masks are provided as base64-encoded PNGs. To use: 1) Decode base64 to get mask image, 2) Resize to match bounding box dimensions, 3) Apply threshold (127/255) to convert to binary mask."
    };

    // Return as a formatted string for better display in the agent
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating segmentation masks:", error);
    return `Error generating segmentation masks: ${error.message}`;
  }
}

/**
 * Compare multiple images
 * @param {GoogleGenAI} ai - Initialized GoogleGenAI instance
 * @param {string} imagePaths - Comma-separated paths to image files
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - Comparison result
 */
async function compareImages(ai, imagePaths, options) {
  try {
    const paths = imagePaths.split(',').map(p => p.trim());
    if (paths.length < 2) {
      return "Please provide at least two image paths separated by commas";
    }

    // Validate all files exist
    for (const imagePath of paths) {
      await fs.access(imagePath);
    }

    // Create content array with all images and comparison prompt
    const contents = [];

    // Add comparison question
    contents.push(options.question || "What are the differences between these images?");

    // Add all images to the content
    for (const imagePath of paths) {
      const ext = path.extname(imagePath).toLowerCase();
      let mimeType;

      if (['.jpg', '.jpeg'].includes(ext)) mimeType = 'image/jpeg';
      else if (ext === '.png') mimeType = 'image/png';
      else if (ext === '.webp') mimeType = 'image/webp';
      else if (ext === '.heic') mimeType = 'image/heic';
      else if (ext === '.heif') mimeType = 'image/heif';
      else if (ext === '.gif') mimeType = 'image/gif'; // Note: GIF might not be fully supported by Gemini
      else return `Unsupported image format: ${ext} for file ${imagePath}. Supported formats are: JPG, PNG, WEBP, HEIC, HEIF`;

      // For first image, use File API (more efficient for reuse)
      if (contents.length === 1) {
        const file = await ai.files.upload({
          file: imagePath,
          config: { mimeType }
        });
        contents.push(createPartFromUri(file.uri, file.mimeType));
      } else {
        // For additional images, use inline data
        const imageData = await fs.readFile(imagePath, { encoding: 'base64' });
        contents.push({
          inlineData: {
            mimeType,
            data: imageData
          }
        });
      }
    }

    // Compare the images
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: createUserContent(contents)
    });

    // Calculate total token usage for all images
    let totalTokens = 0;
    let totalTiles = 0;
    let imageSizes = [];

    for (const imagePath of paths) {
      try {
        const imageBuffer = await fs.readFile(imagePath);
        const imageInfo = await getImageDimensions(imageBuffer);
        totalTokens += imageInfo.tokenEstimate;
        totalTiles += imageInfo.tileCount;
        imageSizes.push({
          path: imagePath,
          width: imageInfo.width,
          height: imageInfo.height,
          tokens: imageInfo.tokenEstimate
        });
      } catch (e) {
        console.warn(`Could not get dimensions for ${imagePath}: ${e.message}`);
      }
    }

    // Create a formatted result object
    const result = {
      comparisonResult: response.text,
      imagesCompared: paths.length,
      question: options.question || "What are the differences between these images?",
      tokenUsage: {
        totalEstimatedTokens: totalTokens,
        totalTiles: totalTiles,
        imageSizes: imageSizes
      }
    };

    // Return as a formatted string for better display in the agent
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error comparing images:", error);
    return `Error comparing images: ${error.message}`;
  }
}

/**
 * Helper function to get image dimensions
 * @param {Buffer} imageBuffer - Image buffer
 * @returns {Promise<{width: number, height: number, tokenEstimate: number, tileCount: number}>} - Image dimensions and token info
 */
async function getImageDimensions(imageBuffer) {
  try {
    // Basic implementation to extract dimensions from PNG and JPEG headers
    // For production use, consider using a library like 'image-size'

    let width = 0;
    let height = 0;

    // Check for PNG signature
    if (imageBuffer.length >= 24 &&
        imageBuffer[0] === 0x89 &&
        imageBuffer[1] === 0x50 &&
        imageBuffer[2] === 0x4E &&
        imageBuffer[3] === 0x47) {
      // PNG format - width is at bytes 16-19, height at 20-23
      width = imageBuffer.readUInt32BE(16);
      height = imageBuffer.readUInt32BE(20);
    }

    // Check for JPEG signature
    else if (imageBuffer.length >= 4 &&
        imageBuffer[0] === 0xFF &&
        imageBuffer[1] === 0xD8) {
      // JPEG format - need to scan through markers
      let offset = 2;
      while (offset < imageBuffer.length - 1) {
        // Check for SOF marker (Start of Frame)
        if (imageBuffer[offset] === 0xFF &&
            (imageBuffer[offset + 1] >= 0xC0 && imageBuffer[offset + 1] <= 0xCF) &&
            imageBuffer[offset + 1] !== 0xC4 &&
            imageBuffer[offset + 1] !== 0xC8) {
          // Height is at offset+5 (2 bytes), width at offset+7 (2 bytes)
          height = imageBuffer.readUInt16BE(offset + 5);
          width = imageBuffer.readUInt16BE(offset + 7);
          break;
        }

        // Move to next marker
        if (imageBuffer[offset] === 0xFF) {
          offset += 2;
          // If marker has length info, skip to next marker
          if (imageBuffer[offset - 1] !== 0xD8 && imageBuffer[offset - 1] !== 0xD9) {
            const length = imageBuffer.readUInt16BE(offset);
            offset += length;
          }
        } else {
          offset += 1;
        }
      }
    }

    // If we couldn't determine dimensions, use default values
    if (width === 0 || height === 0) {
      console.warn("Could not determine image dimensions, using defaults");
      width = 800;
      height = 600;
    }

    // Calculate token usage based on Gemini's rules
    let tokenEstimate = 0;
    let tileCount = 0;

    // If both dimensions are <= 384 pixels, it costs 258 tokens
    if (width <= 384 && height <= 384) {
      tokenEstimate = 258;
      tileCount = 1;
    } else {
      // For larger images, they're tiled into 768x768 pixel tiles
      // Calculate how many tiles we need in each dimension
      const tilesX = Math.ceil(width / 768);
      const tilesY = Math.ceil(height / 768);
      tileCount = tilesX * tilesY;
      tokenEstimate = tileCount * 258;
    }

    return {
      width,
      height,
      tokenEstimate,
      tileCount,
      isLarge: (width > 384 || height > 384)
    };
  } catch (error) {
    console.error("Error getting image dimensions:", error);
    return {
      width: 800,
      height: 600,
      tokenEstimate: 258,
      tileCount: 1,
      isLarge: true
    };
  }
}

/**
 * Enhance a prompt with quality modifiers based on style
 * @param {string} prompt - Original prompt
 * @param {string} style - Style category (portrait, object, motion, wideangle)
 * @returns {string} - Enhanced prompt
 */
function enhancePromptWithQualityModifiers(prompt, style = '') {
  // Base quality modifiers
  let enhancedPrompt = prompt;

  // Add general quality modifiers if not already present
  if (!prompt.toLowerCase().includes('high-quality') &&
      !prompt.toLowerCase().includes('beautiful') &&
      !prompt.toLowerCase().includes('detailed')) {
    enhancedPrompt = `high-quality, beautiful ${enhancedPrompt}`;
  }

  // Add style-specific modifiers
  switch (style.toLowerCase()) {
    case 'portrait':
      if (!containsAny(prompt, ['35mm', '24mm', 'film noir', 'duotone', 'depth of field'])) {
        enhancedPrompt = `${enhancedPrompt}, 35mm portrait, depth of field`;
      }
      break;
    case 'object':
      if (!containsAny(prompt, ['macro', '60mm', '105mm', 'precise focusing', 'controlled lighting'])) {
        enhancedPrompt = `${enhancedPrompt}, macro lens, 60mm, high detail, precise focusing`;
      }
      break;
    case 'motion':
      if (!containsAny(prompt, ['telephoto', '100mm', '400mm', 'fast shutter', 'movement tracking'])) {
        enhancedPrompt = `${enhancedPrompt}, telephoto zoom, fast shutter speed, movement tracking`;
      }
      break;
    case 'wideangle':
      if (!containsAny(prompt, ['wide-angle', '10mm', '24mm', 'long exposure', 'sharp focus'])) {
        enhancedPrompt = `${enhancedPrompt}, wide-angle 10mm, sharp focus`;
      }
      break;
  }

  return enhancedPrompt;
}

/**
 * Check if a string contains any of the given terms
 * @param {string} text - Text to check
 * @param {string[]} terms - Terms to look for
 * @returns {boolean} - True if any term is found
 */
function containsAny(text, terms) {
  const lowerText = text.toLowerCase();
  return terms.some(term => lowerText.includes(term.toLowerCase()));
}

/**
 * Provide style guidance for image generation
 * @param {string} styleType - Type of style guidance requested
 * @returns {string} - Style guidance information
 */
function styleGuide(styleType) {
  const styleGuides = {
    'general': `
      General Image Quality Modifiers:
      - General: high-quality, beautiful, stylized
      - Photos: 4K, HDR, Studio Photo
      - Art/Illustration: by a professional, detailed
    `,
    'aspect': `
      Supported Aspect Ratios:
      - Square (1:1) - Default, good for social media posts
      - Fullscreen (4:3) - Common in media/film, captures more horizontally
      - Portrait fullscreen (3:4) - Captures more vertically
      - Widescreen (16:9) - Modern TV/monitor ratio, good for landscapes
      - Portrait (9:16) - Popular for short-form video, good for tall objects

      Usage: Add aspectRatio option when generating images
    `,
    'portrait': `
      Portrait Photography Style Guide:
      - Lens type: Prime or zoom (24-35mm)
      - Special effects: black and white film, Film noir, Depth of field, duotone
      - Example prompt: "A woman, 35mm portrait, blue and grey duotones"
    `,
    'object': `
      Object Photography Style Guide:
      - Lens type: Macro (60-105mm)
      - Special techniques: High detail, precise focusing, controlled lighting
      - Example prompt: "leaf of a prayer plant, macro lens, 60mm"
    `,
    'motion': `
      Motion Photography Style Guide:
      - Lens type: Telephoto zoom (100-400mm)
      - Special techniques: Fast shutter speed, Action or movement tracking
      - Example prompt: "a winning touchdown, fast shutter speed, movement tracking"
    `,
    'wideangle': `
      Wide-Angle Photography Style Guide:
      - Lens type: Wide-angle (10-24mm)
      - Special techniques: Long exposure times, sharp focus, smooth water/clouds
      - Example prompt: "an expansive mountain range, landscape wide angle 10mm"
    `
  };

  if (styleType.toLowerCase() === 'list') {
    return `Available style guides: ${Object.keys(styleGuides).join(', ')}`;
  }

  return styleGuides[styleType.toLowerCase()] ||
    `Style guide not found. Available guides: ${Object.keys(styleGuides).join(', ')}`;
}


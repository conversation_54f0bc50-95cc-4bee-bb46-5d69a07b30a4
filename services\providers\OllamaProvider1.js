// providers/ollamaProvider.js

export class OllamaProvider {
  /**
   * Constructs an OllamaProvider.
   * @param {string} baseUrl - The base URL of the Ollama server (e.g., 'http://localhost:11434').
   * @param {string} modelName - The name of the Ollama model to use (e.g., 'llama2', 'mistral').
   */
  constructor(baseUrl = 'http://localhost:11434', modelName = 'llama2') {
    if (!baseUrl) {
      throw new Error('Error: Ollama base URL was not provided to OllamaProvider.');
    }
    if (!modelName) {
      throw new Error('Error: Ollama model name was not provided to OllamaProvider.');
    }
    this.baseUrl = baseUrl;
    this.modelName = modelName;
    this.client = axios.create({ baseURL: this.baseUrl });
  }

  /**
   * Converts the prompt structure from LLMClient to Ollama's messages format.
   * @param {object} prompt - The prompt object with `history` and `contents`.
   * @returns {Array<object>} An array of messages in Ollama's format.
   */
  _formatContentsForOllama(prompt) {
    let messages = [];

    // Add existing history from LLMClient's format
    if (prompt.history && Array.isArray(prompt.history)) {
      messages = messages.concat(prompt.history.map(part => {
        // Assuming parts is always [{ text: '...' }]
        const content = part.parts.map(p => p.text).join('\n');
        return { role: part.role, content: content };
      }));
    }

    // Add the current user content if it's separate from history
    if (prompt.contents && Array.isArray(prompt.contents)) {
      prompt.contents.forEach(part => {
        const content = part.parts.map(p => p.text).join('\n');
        messages.push({ role: part.role, content: content });
      });
    }

    return messages;
  }

  /**
   * Generates content from the Ollama model without streaming.
   * @param {object} prompt - The prompt object.
   * @param {object} [generationConfigFromTool={}] - Additional generation configuration.
   * @returns {Promise<string>} The generated text.
   */
  async generateContent(prompt, generationConfigFromTool = {}) {
    try {
      return await this.retryWithBackoff(async () => {
        const messages = this._formatContentsForOllama(prompt);

        const options = {
          temperature: generationConfigFromTool.temperature || prompt.temperature || 0.7,
          top_p: generationConfigFromTool.topP || prompt.topP || 0.95, // Ollama uses top_p
          top_k: generationConfigFromTool.topK || prompt.topK || 40, // Ollama uses top_k
          num_predict: generationConfigFromTool.maxOutputTokens || prompt.maxOutputTokens || 2048, // Ollama uses num_predict
          // Ollama also has other options like num_ctx, repeat_last_n, etc.
          // Add them here if needed based on your Agent's config.
        };

        const requestBody = {
          model: this.modelName,
          messages: messages,
          options: options,
          stream: false, // Ensure non-streaming
        };

        console.log(`OllamaProvider: Sending non-streaming request to /api/chat for model ${this.modelName}`);
        const response = await this.client.post('/api/chat', requestBody);

        // Ollama chat response structure
        if (response.data && response.data.message && response.data.message.content) {
          return response.data.message.content;
        } else {
          console.warn('OllamaProvider: Unexpected response structure or no text content found in generateContent response:', JSON.stringify(response.data, null, 2));
          return '';
        }
      });
    } catch (error) {
      console.error('Error calling Ollama in OllamaProvider.generateContent:', error.message || error.toString());
      throw new Error(`Ollama API error in generateContent: ${error.message || error.toString()}`);
    }
  }

  /**
   * Generates content from the Ollama model with streaming.
   * @param {object} prompt - The prompt object.
   * @param {object} [generationConfigFromTool={}] - Additional generation configuration.
   * @param {function} onChunk - Callback function for each streamed chunk.
   * @returns {Promise<string>} The complete generated text.
   */
  async generateContentStream(prompt, generationConfigFromTool = {}, onChunk) {
    try {
      const messages = this._formatContentsForOllama(prompt);

      const options = {
        temperature: generationConfigFromTool.temperature || prompt.temperature || 0.7,
        top_p: generationConfigFromTool.topP || prompt.topP || 0.8,
        top_k: generationConfigFromTool.topK || prompt.topK || 40,
        num_predict: generationConfigFromTool.maxOutputTokens || prompt.maxOutputTokens || 2048,
      };

      const requestBody = {
        model: this.modelName,
        messages: messages,
        options: options,
        stream: true, // Ensure streaming
      };

      console.log(`OllamaProvider: Sending streaming request to /api/chat for model ${this.modelName}`);
      const response = await this.client.post('/api/chat', requestBody, { responseType: 'stream' });

      let fullResponse = '';
      const stream = response.data;

      for await (const chunk of stream) {
        try {
          const chunkStr = chunk.toString('utf8');
          // Ollama streams JSON objects, potentially multiple per chunk
          const jsonStrings = chunkStr.split('\n').filter(s => s.trim() !== '');

          for (const jsonStr of jsonStrings) {
            const data = JSON.parse(jsonStr);
            if (data.message && data.message.content) {
              const chunkText = data.message.content;
              fullResponse += chunkText;
              if (onChunk && typeof onChunk === 'function') {
                onChunk(chunkText);
              }
            }
            // Check for done signal
            if (data.done) {
                break; // Exit the loop if the stream is done
            }
          }
        } catch (parseError) {
          console.error('OllamaProvider: Error parsing stream chunk:', parseError.message, 'Chunk:', chunk.toString('utf8'));
          // Continue, as some chunks might be incomplete JSON
        }
      }
      return fullResponse;
    } catch (error) {
      console.error('Error generating content stream in OllamaProvider:', error.message || error.toString());
      throw new Error(`Ollama API streaming error: ${error.message || error.toString()}`);
    }
  }

  async retryWithBackoff(operation, maxRetries = 3, initialDelay = 1000) {
    let retries = 0;
    let delay = initialDelay;
    while (retries < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        // Ollama errors might not have a .status, rely on message or axios error codes
        const isOverloaded =
          error.code === 'ECONNREFUSED' || // Connection refused (Ollama not running)
          error.response?.status === 503 || // Service Unavailable
          error.response?.status === 429 || // Too Many Requests
          error.message?.includes('UNAVAILABLE') ||
          error.message?.includes('overloaded') ||
          error.message?.includes('rate limit');

        if (!isOverloaded || retries >= maxRetries - 1) {
          throw error;
        }
        retries++;
        console.log(`OllamaProvider: API issue. Retry ${retries}/${maxRetries} after ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * 2 + Math.random() * 1000, 30000);
      }
    }
  }
}
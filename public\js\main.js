// Toggle sidebar between collapsed (t1) and expanded (t2) states on desktop
        // or show/hide on mobile
        document.getElementById('toggleSidebar').addEventListener('click', () => {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            // Check if we're on mobile
            if (window.innerWidth < 769) {
                // Mobile behavior - toggle visibility
                if (sidebar.style.display === 'none' || sidebar.style.display === '') {
                    // Show sidebar
                    sidebar.style.display = 'flex';
                    sidebar.classList.add('expanded');
                    sidebar.classList.remove('collapsed');
                    overlay.style.display = 'block';
                    sidebar.style.animation = 'slideIn 0.3s forwards';
                    document.body.style.overflow = 'hidden';
                } else {
                    // Hide sidebar
                    closeMobileSidebar();
                }
            } else {
                // Desktop behavior - toggle expanded/collapsed
                sidebar.classList.toggle('expanded');
                sidebar.classList.toggle('collapsed');
            }
        });

        // Define the animation in a style tag if it doesn't exist
        if (!document.getElementById('sidebarAnimation')) {
            const style = document.createElement('style');
            style.id = 'sidebarAnimation';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(-100%); }
                    to { transform: translateX(0); }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); }
                    to { transform: translateX(-100%); }
                }
            `;
            document.head.appendChild(style);
        }

        // Mobile menu toggle - use the same toggle function
        document.getElementById('mobileMenuToggle').addEventListener('click', () => {
            // Trigger the same function as toggleSidebar
            document.getElementById('toggleSidebar').click();
        });

        // Close sidebar when clicking on overlay
        document.getElementById('sidebarOverlay').addEventListener('click', closeMobileSidebar);

        // Function to close mobile sidebar
        function closeMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            // Add slide out animation
            sidebar.style.animation = 'slideOut 0.3s forwards';

            // Hide overlay immediately
            overlay.style.display = 'none';

            // Wait for animation to complete before hiding sidebar
            setTimeout(() => {
                sidebar.style.display = 'none';
                // Reset animation
                sidebar.style.animation = '';
            }, 300);

            // Restore body scrolling
            document.body.style.overflow = '';
        }

        // Handle window resize for responsive design
        window.addEventListener('resize', () => {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (window.innerWidth >= 769) {
                // Desktop view
                sidebar.style.display = 'flex';
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            } else {
                // Mobile view - hide sidebar and overlay
                sidebar.style.display = 'none';
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }
        });

        // Function to generate a unique session ID
        function generateSessionId() {
            // Check if we already have a session ID in localStorage
            let sessionId = localStorage.getItem('virtra_session_id');

            // If not, create a new one
            if (!sessionId) {
                sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('virtra_session_id', sessionId);
                console.log('Generated new session ID:', sessionId);
            } else {
                console.log('Using existing session ID:', sessionId);
            }

            return sessionId;
        }

        // Audio streaming disabled

        document.addEventListener('DOMContentLoaded', () => {
            const chatDisplay = document.getElementById('chatDisplay');
            const textInput = document.getElementById('textInput');
            const sendButton = document.getElementById('sendButton');
            const recordButton = document.getElementById('recordButton');
            const imageUploadInput = document.getElementById('imageUpload');
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const clearImagesButton = document.getElementById('clearImagesButton'); 

            const documentUploadInput = document.getElementById('documentUpload');
            const documentPreviewContainer = document.getElementById('documentPreviewContainer');
            const clearDocumentButton = document.getElementById('clearDocumentButton');

            let selectedImageDataUrls = []; // Array to store Data URLs of selected images
            let uploadedDocumentPath = null; // To store the path of the uploaded document
            let uploadedDocumentName = null; // To store the original name of the uploaded document

            // Generate or retrieve session ID
            let sessionId = generateSessionId();
            console.log('Active session ID:', sessionId);

            // Function to load conversation history
            async function loadConversationHistory() {
                try {
                    const response = await fetch('/api/agent/conversations');
                    if (!response.ok) {
                        throw new Error('Failed to load conversations');
                    }

                    const data = await response.json();
                    const conversationsList = document.getElementById('conversationsList');

                    // Clear the loading message
                    conversationsList.innerHTML = '';

                    if (data.conversations && data.conversations.length > 0) {
                        // Add each conversation to the list
                        data.conversations.forEach(conv => {
                            const formattedDate = new Date(conv.lastUpdated).toLocaleDateString();
                            const isActive = conv.sessionId === sessionId;

                            const listItem = document.createElement('li');
                            listItem.className = `flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800 cursor-pointer ${
                                isActive ? 'sidebar-item-active bg-gray-800' : ''
                            }`;
                            listItem.dataset.sessionId = conv.sessionId;

                            listItem.innerHTML = `
                                <i class="fas fa-comment text-gray-400"></i>
                                <span class="sidebar-item-text flex-grow truncate">${conv.title}</span>
                                <span class="text-xs text-gray-500">${formattedDate}</span>
                            `;

                            // Add click event to switch to this conversation
                            listItem.addEventListener('click', () => switchConversation(conv.sessionId));

                            conversationsList.appendChild(listItem);
                        });
                    } else {
                        // No conversations found
                        conversationsList.innerHTML = `
                            <li class="flex items-center justify-center py-4 text-gray-400">
                                <span class="text-xs">No conversations yet</span>
                            </li>
                        `;
                    }
                } catch (error) {
                    console.error('Error loading conversations:', error);
                    document.getElementById('conversationsList').innerHTML = `
                        <li class="flex items-center justify-center py-4 text-gray-400">
                            <span class="text-xs">Error loading conversations</span>
                        </li>
                    `;
                }
            }

            // Function to switch to a different conversation
            async function switchConversation(newSessionId) {
                if (newSessionId === sessionId) return; // Already on this conversation

                // Update the active session ID
                sessionId = newSessionId;
                localStorage.setItem('virtra_session_id', sessionId);
                console.log('Switched to session ID:', sessionId);

                // Clear the chat display
                chatDisplay.innerHTML = '';

                // Show loading message
                const loadingMsg = document.createElement('div');
                loadingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                loadingMsg.innerHTML = '<div class="inline-block animate-pulse">Loading conversation...</div>';
                chatDisplay.appendChild(loadingMsg);

                try {
                    // Load the conversation memory
                    const response = await fetch(`/api/agent/memory?sessionId=${sessionId}`);
                    const data = await response.json();

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    // Display the messages
                    if (data.messages && data.messages.length > 0) {
                        data.messages.forEach(msg => {
                            // Use the message timestamp if available, otherwise use current time
                            const msgTimestamp = msg.timestamp ? new Date(msg.timestamp) : new Date();
                            addMessage(msg.text, msg.role === 'user', 'normal', null, null, msgTimestamp); // Added null for screenshotPath
                        });

                        // Apply time display settings after loading all messages
                        if (window.timeDisplayManager) {
                            window.timeDisplayManager.applySettings();
                        }
                    } else {
                        // No messages in this conversation yet
                        addMessage('This is the start of your conversation. How can I help you today?', false, 'normal', null, null, new Date()); // Added null
                    }

                    // Update the active conversation in the sidebar
                    loadConversationHistory();
                } catch (error) {
                    console.error('Error loading conversation memory:', error);
                    chatDisplay.removeChild(loadingMsg);
                    addMessage('Error loading conversation. Please try again.', false, 'normal', null, null, new Date()); // Added null
                }
            }

            // Function to start a new conversation
            function startNewConversation() {
                // Generate a new session ID
                sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('virtra_session_id', sessionId);
                console.log('Started new conversation with session ID:', sessionId);

                // Clear the chat display
                chatDisplay.innerHTML = '';

                // Add welcome message
                addMessage('This is the start of a new conversation. How can I help you today?', false, 'normal', null, null, new Date()); // Added null

                // Update the conversation list
                loadConversationHistory();
            }

            // Add event listeners to the new chat buttons
            document.getElementById('newChatButton').addEventListener('click', startNewConversation);
            document.getElementById('newChatButtonCollapsed').addEventListener('click', startNewConversation);

            let mediaRecorder;
            let audioChunks = [];
            let isRecording = false;

            // Add a message to the chat display
            function addMessage(text, isUser = false, messageType = 'normal', imageUrls = null, screenshotPath = null, timestamp = new Date()) {
                const messageContainer = document.createElement('div');
                messageContainer.className = 'max-w-3xl mx-auto mb-6 w-full';

                // Function to parse markdown and apply syntax highlighting
                function parseMarkdown(content) {
                    try {
                        // Parse markdown using marked library
                        const parsedContent = marked.parse(content);

                        // Create a temporary div to manipulate the HTML
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = parsedContent;

                        // Apply syntax highlighting to code blocks if hljs is available
                        if (typeof hljs !== 'undefined') {
                            tempDiv.querySelectorAll('pre code').forEach((block) => {
                                try {
                                    // Detect language from class
                                    let language = '';
                                    if (block.className) {
                                        const match = block.className.match(/language-(\w+)/);
                                        if (match) {
                                            language = match[1].toUpperCase();
                                        }
                                    }

                                    // Add language label if detected
                                    if (language && block.parentNode) {
                                        const labelDiv = document.createElement('div');
                                        labelDiv.className = 'code-language-label';
                                        labelDiv.textContent = language;
                                        block.parentNode.insertBefore(labelDiv, block);
                                    }

                                    // Apply syntax highlighting
                                    hljs.highlightElement(block);
                                } catch (highlightError) {
                                    console.warn('Error highlighting code block:', highlightError);
                                    // Add a simple styling if highlighting fails
                                    block.classList.add('fallback-highlight');
                                }
                            });
                        } else {
                            console.warn('highlight.js not loaded, using fallback styling');
                            // Add fallback styling to code blocks
                            tempDiv.querySelectorAll('pre code').forEach((block) => {
                                block.classList.add('fallback-highlight');
                            });
                        }

                        return tempDiv.innerHTML;
                    } catch (error) {
                        console.error('Error parsing markdown:', error);
                        // Return the original content if parsing fails
                        return `<div class="text-red-500">Error formatting message</div><div>${content}</div>`;
                    }
                }

                if (messageType === 'system') {
                    // System message (for notifications, persona changes, etc.)
                    const systemMessage = document.createElement('div');
                    systemMessage.className = 'system-message text-center py-2 px-4 bg-gray-800 text-gray-300 rounded-lg';
                    systemMessage.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${text}`;
                    messageContainer.appendChild(systemMessage);
                } else if (isUser) {
                    // User message
                    const userBubble = document.createElement('div');
                    userBubble.className = 'user-bubble ml-12 mr-0 user-message message-bubble';
                    userBubble.innerHTML = `<div class="message-content"><p>${text}</p></div>`;

                    // Add timestamp
                    if (window.timeDisplayManager) {
                        window.timeDisplayManager.addTimestampToMessage(userBubble, timestamp);
                    } else {
                        // Fallback if timeDisplayManager is not available
                        const timeEl = document.createElement('span');
                        timeEl.className = 'message-time';
                        timeEl.setAttribute('data-timestamp', timestamp.toISOString());
                        timeEl.textContent = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        userBubble.querySelector('.message-content').appendChild(timeEl);
                    }

                    messageContainer.appendChild(userBubble);
                } else {
                    // Assistant message
                    const assistantMessage = document.createElement('div');
                    assistantMessage.className = 'flex items-start';

                    // Parse markdown in assistant messages
                    const parsedContent = parseMarkdown(text);

                    assistantMessage.innerHTML = `
                        <div class="mr-4 pt-1 flex-shrink-0">
                            <span class="blue-star">✧</span>
                        </div>
                        <div class="assistant-message markdown-content w-full message-bubble">
                            <div class="message-content">${parsedContent}</div>
                        </div>
                    `;

                    // Add timestamp
                    const messageBubble = assistantMessage.querySelector('.message-bubble');
                    const messageContentDiv = messageBubble.querySelector('.message-content'); // Get the actual content div

                    if (window.timeDisplayManager && messageBubble) {
                        window.timeDisplayManager.addTimestampToMessage(messageBubble, timestamp);
                    } else if (messageContentDiv) { // Ensure messageContentDiv exists
                        // Fallback if timeDisplayManager is not available
                        const timeEl = document.createElement('span');
                        timeEl.className = 'message-time';
                        timeEl.setAttribute('data-timestamp', timestamp.toISOString());
                        timeEl.textContent = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        messageContentDiv.appendChild(timeEl); // Append time to the content div
                    }

                    // Add images if imageUrls are provided
                    if (imageUrls && Array.isArray(imageUrls) && imageUrls.length > 0) {
                        const imagesContainer = document.createElement('div');
                        imagesContainer.className = 'mt-3 flex flex-col items-start gap-2'; // Use flex-col for vertical stacking, items-start
                        imageUrls.forEach(url => {
                            const imgElement = document.createElement('img');
                            imgElement.src = url;
                            imgElement.alt = 'Generated Image';
                            // Styling for images: responsive, rounded, with a border
                            imgElement.className = 'max-w-full md:max-w-lg rounded-lg border border-gray-700 shadow-md'; 
                            imgElement.style.maxHeight = '500px'; // Max height to prevent overly large images
                            imgElement.style.objectFit = 'contain'; // Ensure entire image is visible
                            imagesContainer.appendChild(imgElement);
                        });
                        // Append images container after the text content, inside messageContentDiv
                        if (messageContentDiv) {
                            messageContentDiv.appendChild(imagesContainer);
                        }
                    }

                    // Add screenshot if screenshotPath is provided
                    if (screenshotPath && typeof screenshotPath === 'string') {
                        const screenshotContainer = document.createElement('div');
                        screenshotContainer.className = 'mt-3 flex flex-col items-start gap-2';
                        
                        const imgElement = document.createElement('img');
                        
                        let imgSrc = screenshotPath.replace(/\\/g, '/'); // Normalize to forward slashes
                        if (imgSrc.startsWith('public/')) {
                            imgSrc = imgSrc.substring('public/'.length); // Remove 'public/' prefix
                        }
                        if (!imgSrc.startsWith('/')) {
                            imgSrc = '/' + imgSrc; // Ensure it's a root-relative path (e.g., /screenshots/image.png)
                        }
                        
                        imgElement.src = imgSrc;
                        imgElement.alt = 'Screenshot';
                        imgElement.className = 'max-w-full md:max-w-lg rounded-lg border border-gray-700 shadow-md';
                        imgElement.style.maxHeight = '500px';
                        imgElement.style.objectFit = 'contain';
                        screenshotContainer.appendChild(imgElement);

                        if (messageContentDiv) {
                            messageContentDiv.appendChild(screenshotContainer);
                        }
                    }

                    const optionsButton = document.createElement('div');
                    optionsButton.className = 'options-button absolute right-0 top-0';
                    optionsButton.innerHTML = `
                        <button class="p-2 text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    `;

                    messageContainer.appendChild(assistantMessage);
                    messageContainer.appendChild(optionsButton);
                    messageContainer.className += ' relative';
                }

                chatDisplay.appendChild(messageContainer);

                // Scroll to bottom
                chatDisplay.scrollTop = chatDisplay.scrollHeight;
            }

            // Handle image selection
            imageUploadInput.addEventListener('change', (event) => {
                selectedImageDataUrls = []; // Clear previous selections
                imagePreviewContainer.innerHTML = ''; // Clear previous previews

                const files = event.target.files;
                if (files.length === 0) {
                    imagePreviewContainer.classList.add('hidden');
                    clearImagesButton.classList.add('hidden');
                    return;
                }

                let validFilesFound = false;
                Array.from(files).forEach(file => {
                    if (file && file.type.startsWith('image/')) {
                        validFilesFound = true;
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            selectedImageDataUrls.push(e.target.result);

                            const imgPreview = document.createElement('img');
                            imgPreview.src = e.target.result;
                            imgPreview.alt = "Image preview";
                            imgPreview.className = "max-h-20 max-w-xs object-contain rounded border border-gray-600"; // Adjusted size
                            imagePreviewContainer.appendChild(imgPreview);
                        };
                        reader.readAsDataURL(file);
                    }
                });

                if (validFilesFound) {
                    imagePreviewContainer.classList.remove('hidden');
                    clearImagesButton.classList.remove('hidden');
                } else {
                    imagePreviewContainer.classList.add('hidden');
                    clearImagesButton.classList.add('hidden');
                    if (files.length > 0) { // if files were selected but none were valid images
                        alert('Please select valid image files.');
                    }
                }
            });

            // Handle clear images button
            clearImagesButton.addEventListener('click', () => {
                selectedImageDataUrls = [];
                imagePreviewContainer.innerHTML = ''; // Clear all previews
                imagePreviewContainer.classList.add('hidden');
                clearImagesButton.classList.add('hidden');
                imageUploadInput.value = ''; // Reset file input
            });

            // Handle document selection
            documentUploadInput.addEventListener('change', async (event) => {
                const file = event.target.files[0];
                if (!file) {
                    return;
                }

                documentPreviewContainer.innerHTML = ''; // Clear previous preview

                const formData = new FormData();
                formData.append('document', file);

                // Show a temporary "Uploading..." message in the preview container
                const uploadingText = document.createElement('p');
                uploadingText.textContent = `Uploading ${file.name}...`;
                uploadingText.className = 'text-sm text-gray-400';
                documentPreviewContainer.appendChild(uploadingText);
                documentPreviewContainer.classList.remove('hidden');

                try {
                    const response = await fetch('/api/upload/document', {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ error: 'Failed to upload document. Server error.' }));
                        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    uploadedDocumentPath = data.filePath;
                    uploadedDocumentName = data.originalName;

                    documentPreviewContainer.innerHTML = ''; // Clear "Uploading..."
                    const previewText = document.createElement('p');
                    previewText.textContent = `Attached: ${uploadedDocumentName}`;
                    previewText.className = 'text-sm text-green-400 p-2 bg-gray-700 rounded';
                    documentPreviewContainer.appendChild(previewText);
                    documentPreviewContainer.classList.remove('hidden');
                    clearDocumentButton.classList.remove('hidden');

                } catch (error) {
                    console.error('Error uploading document:', error);
                    documentPreviewContainer.innerHTML = ''; // Clear "Uploading..."
                    const errorText = document.createElement('p');
                    errorText.textContent = `Error uploading ${file.name}: ${error.message}`;
                    errorText.className = 'text-sm text-red-400';
                    documentPreviewContainer.appendChild(errorText);
                    documentPreviewContainer.classList.remove('hidden');
                    
                    uploadedDocumentPath = null;
                    uploadedDocumentName = null;
                    clearDocumentButton.classList.add('hidden');
                }
            });

            // Handle clear document button
            clearDocumentButton.addEventListener('click', () => {
                uploadedDocumentPath = null;
                uploadedDocumentName = null;
                documentPreviewContainer.innerHTML = '';
                documentPreviewContainer.classList.add('hidden');
                clearDocumentButton.classList.add('hidden');
                documentUploadInput.value = ''; // Reset file input
            });

            // Send message
            async function sendMessage() {
                let text = textInput.value.trim();
                const originalUserText = text; // Keep original text for display

                if (!text && selectedImageDataUrls.length === 0 && !uploadedDocumentPath) return; // Nothing to send

                // If there are images but no text, use a default prompt
                if (selectedImageDataUrls.length > 0 && !text && !uploadedDocumentPath) {
                    text = selectedImageDataUrls.length > 1 ? "Compare these images." : "Describe this image.";
                }
                // If there is a document but no text, use a default prompt
                if (uploadedDocumentPath && !text && selectedImageDataUrls.length === 0) {
                    text = `Analyze the uploaded document: ${uploadedDocumentName}`;
                }
                
                let displayMessage = originalUserText;
                let attachmentsInfo = [];
                if (selectedImageDataUrls.length > 0) {
                    attachmentsInfo.push(`${selectedImageDataUrls.length} image(s)`);
                }
                if (uploadedDocumentName) {
                    attachmentsInfo.push(`document: ${uploadedDocumentName}`);
                }

                if (attachmentsInfo.length > 0) {
                    displayMessage = originalUserText ? `${originalUserText} (${attachmentsInfo.join(', ')})` : `(${attachmentsInfo.join(', ')}) attached`;
                }


                if (originalUserText || selectedImageDataUrls.length > 0 || uploadedDocumentPath) { 
                    addMessage(displayMessage, true, 'normal', null, null, new Date());
                }
                
                textInput.value = '';

                // Prepare message payload for the agent
                let agentMessagePayload = text; 

                try {
                    // Show thinking indicator
                    const thinkingMsg = document.createElement('div');
                    thinkingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                    thinkingMsg.innerHTML = '<div class="inline-block animate-pulse">Thinking...</div>';
                    chatDisplay.appendChild(thinkingMsg);

                    try {
                        // Check if this is a code request
                        const isCodeRequest = agentMessagePayload.toLowerCase().includes('write') &&
                            (agentMessagePayload.toLowerCase().includes('code') ||
                             agentMessagePayload.toLowerCase().includes('html') ||
                             agentMessagePayload.toLowerCase().includes('css') ||
                             agentMessagePayload.toLowerCase().includes('javascript') ||
                             agentMessagePayload.toLowerCase().includes('python') ||
                             agentMessagePayload.toLowerCase().includes('function'));
                        
                        const requestContext = {
                            generateSpeech: true,
                            sessionId: sessionId,
                            isCodeRequest: isCodeRequest,
                            personaId: personaSelector.value, // Include current persona ID
                        };

                        if (selectedImageDataUrls.length > 0) {
                            requestContext.imageDataUrls = selectedImageDataUrls; 
                        }
                        if (uploadedDocumentPath) {
                            requestContext.uploadedDocumentPath = uploadedDocumentPath;
                            requestContext.uploadedDocumentName = uploadedDocumentName;
                             // Append info to the agent message payload if not already part of user's text
                            if (!agentMessagePayload.toLowerCase().includes(uploadedDocumentName.toLowerCase()) && !agentMessagePayload.toLowerCase().includes(uploadedDocumentPath.toLowerCase())) {
                                agentMessagePayload += ` (Context: User uploaded document '${uploadedDocumentName}' located at server path '${uploadedDocumentPath}')`;
                            }
                        }

                        // Handle text message
                        const response = await fetch('/api/agent/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                message: agentMessagePayload, // This is the user's textual query
                                sessionId: sessionId,
                                context: requestContext
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        console.log('Response data:', data);

                        // Remove thinking indicator
                        chatDisplay.removeChild(thinkingMsg);

                        // Add agent response to chat, including any images or screenshots
                        addMessage(data.text, false, 'normal', data.imageUrls, data.screenshotPath, new Date());

                        // Use streaming audio for better latency
                        const currentPersona = personaSelector.value;
                        console.log('Using streaming audio with persona:', currentPersona);

                        // TTS is disabled - no audio playback
                    } catch (chatError) {
                        console.error('Error processing chat request:', chatError);
                        // Remove thinking indicator if it exists
                        if (thinkingMsg && thinkingMsg.parentNode) {
                            chatDisplay.removeChild(thinkingMsg);
                        }
                        // Show error message
                        addMessage('Sorry, I encountered an error processing your request. Please try again.', false, 'normal', null, null, new Date()); // Added null
                    }
                } catch (error) { // General error for the outer try
                    console.error('Error processing request:', error);
                    addMessage('Sorry, I encountered an error processing your request.', false, 'normal', null, null, new Date()); // Added null
                } finally {
                    // Clear selected images after sending
                    if (selectedImageDataUrls.length > 0) {
                        clearImagesButton.click(); 
                    }
                    // Clear uploaded document after sending
                    if (uploadedDocumentPath) {
                        clearDocumentButton.click();
                    }
                }
            }

            // Handle send button click
            sendButton.addEventListener('click', sendMessage);

            // Handle enter key in text input
            textInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Handle record button click
            recordButton.addEventListener('click', async () => {
                const recordingIndicator = document.getElementById('recordingIndicator');

                if (isRecording) {
                    // Stop recording
                    mediaRecorder.stop();
                    recordButton.classList.remove('recording-pulse');
                    recordingIndicator.classList.add('hidden');
                    isRecording = false;

                    // Change the microphone icon back to normal
                    recordButton.querySelector('i').className = 'fas fa-microphone';
                } else {
                    // Start recording
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        mediaRecorder = new MediaRecorder(stream);
                        audioChunks = [];

                        mediaRecorder.addEventListener('dataavailable', (event) => {
                            audioChunks.push(event.data);
                        });

                        mediaRecorder.addEventListener('stop', async () => {
                            try {
                                // Show transcribing indicator
                                const transcribingMsg = document.createElement('div');
                                transcribingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                                transcribingMsg.innerHTML = '<div class="inline-flex items-center"><i class="fas fa-cog fa-spin mr-2"></i> Transcribing audio...</div>';
                                chatDisplay.appendChild(transcribingMsg);

                                // Create audio blob
                                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });

                                // Create form data
                                const formData = new FormData();
                                formData.append('audio', audioBlob);

                                // Transcribe audio
                                const transcribeResponse = await fetch('/api/transcribe', {
                                    method: 'POST',
                                    body: formData
                                });

                                const transcribeData = await transcribeResponse.json();

                                // Remove transcribing indicator
                                chatDisplay.removeChild(transcribingMsg);

                                if (transcribeData.text) {
                                    // Add transcription to chat
                                    addMessage(transcribeData.text, true, 'normal', null, null, new Date()); // Added null

                                    // Show thinking indicator
                                    const thinkingMsg = document.createElement('div');
                                    thinkingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                                    thinkingMsg.innerHTML = '<div class="inline-block animate-pulse">Thinking...</div>';
                                    chatDisplay.appendChild(thinkingMsg);

                                    // Process with agent
                                    const agentResponse = await fetch('/api/agent/voice', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            transcription: transcribeData.text,
                                            sessionId: sessionId,
                                            context: {
                                                generateSpeech: true,
                                                sessionId: sessionId,
                                                personaId: personaSelector.value // Include current persona ID
                                            }
                                        })
                                    });

                                    const agentData = await agentResponse.json();

                                    // Remove thinking indicator
                                    chatDisplay.removeChild(thinkingMsg);

                                    // Add agent response to chat
                                    addMessage(agentData.text, false, 'normal', agentData.imageUrls, agentData.screenshotPath, new Date()); // Added screenshotPath

                                    // Use streaming audio for better latency
                                    console.log('Using streaming audio for voice response');

                                    // TTS is disabled - no audio playback
                                } else {
                                    addMessage("I couldn't understand what you said. Please try again.", false, 'normal', null, null, new Date()); // Added null
                                }
                            } catch (error) {
                                console.error('Error processing voice:', error);
                                addMessage('Sorry, I encountered an error processing your voice input.', false, 'normal', null, null, new Date()); // Added null
                            }

                            // Stop all tracks
                            stream.getTracks().forEach(track => track.stop());
                        });

                        // Start recording
                        mediaRecorder.start();
                        recordButton.classList.add('recording-pulse');
                        recordingIndicator.classList.remove('hidden');
                        isRecording = true;

                        // Change the microphone icon to indicate recording
                        recordButton.querySelector('i').className = 'fas fa-microphone-alt';
                    } catch (error) {
                        console.error('Error accessing microphone:', error);
                        addMessage('Error accessing microphone. Please make sure you have granted permission to use the microphone.', false, 'normal', null, null, new Date()); // Added null
                    }
                }
            });

            // Load conversation history
            loadConversationHistory();

            // Add a welcome message with TTS
            setTimeout(async () => {
                // Check if we need to load existing conversation or show welcome message
                try {
                    // Try to load the current conversation memory
                    const memoryResponse = await fetch(`/api/agent/memory?sessionId=${sessionId}`);
                    const memoryData = await memoryResponse.json();

                    if (memoryData.messages && memoryData.messages.length > 0) {
                        // We have existing messages, display them
                        memoryData.messages.forEach(msg => {
                            addMessage(msg.text, msg.role === 'user', 'normal', null, null, msg.timestamp ? new Date(msg.timestamp) : new Date()); // Added null
                        });
                        return; // Skip welcome message
                    }
                } catch (error) {
                    console.error('Error checking conversation memory:', error);
                    // Continue with welcome message
                }

                // Show a loading message first
                const loadingMsg = document.createElement('div');
                loadingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                loadingMsg.innerHTML = '<div class="inline-block animate-pulse">Initializing Varjis...</div>';
                chatDisplay.appendChild(loadingMsg);

                try {
                    // Get a welcome message from the agent with TTS
                    const response = await fetch('/api/agent/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: "__welcome__",
                            sessionId: sessionId,
                            context: {
                                generateSpeech: true,
                                sessionId: sessionId,
                                personaId: personaSelector.value // Include current persona ID
                            }
                        })
                    });

                    const data = await response.json();

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    // Add the welcome message
                    addMessage(data.text || "Welcome to Varjis! You can type a message, or use voice input. How can I help you today?", false, 'normal', data.imageUrls, data.screenshotPath, new Date()); // Added screenshotPath

                    // Use streaming audio for better latency
                    console.log('Using streaming audio for welcome message');

                    // TTS is disabled - no audio playback
                } catch (error) {
                    console.error('Error getting welcome message:', error);

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    // Fallback welcome message
                    addMessage("Welcome to Varjis! You can type a message, or use voice input. How can I help you today?", false, 'normal', null, null, new Date()); // Added null
                }
            }, 500);

            // Persona switching functionality
            const personaSelector = document.getElementById('personaSelector');
            const currentPersonaDisplay = document.getElementById('currentPersona');

            // Load current persona on page load
            // async function loadCurrentPersona() {
            //     try {
            //         const response = await fetch('/api/agent/personas/current');
            //         if (response.ok) {
            //             const data = await response.json();
            //             currentPersonaDisplay.textContent = data.currentPersona.charAt(0).toUpperCase() + data.currentPersona.slice(1);
            //             personaSelector.value = data.currentPersona;
            //         }
            //     } catch (error) {
            //         console.error('Error loading current persona:', error);
            //     }
            // }

            // Load current persona on page load
            loadCurrentPersona();
        });

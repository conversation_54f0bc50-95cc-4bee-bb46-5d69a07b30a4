// services/Agent.js

import path from 'path';
import crypto from 'crypto'; // For generating unique IDs
import Persona from './Persona.js';
import LLMClient from './LLMClient.js'; // Ensure this points to the correct LLMClient.js
import { HybridConversationMemory } from './memory.js';
import { synthesizeSpeech } from './textToSpeech-simplified.js';
import { getRandomFollowUpPhrase } from './response-variations.js';
import { cleanTextForTTS } from './agentLogic-simplified.js';
import { generateEmbedding } from './embeddingService.js'; // RAG
import { upsertToPinecone, queryFromPinecone } from './vectorStoreService.js'; // RAG

/**
 * @typedef {object} AgentConfig
 * @property {Persona} persona - The persona instance for the agent.
 * @property {LLMClient} llmClient - The LLM client instance.
 * @property {HybridConversationMemory} memory - The conversation memory instance.
 * @property {object} [agentGenerationConfig] - Optional LLM generation config specific to this agent.
 */

/**
 * @typedef {object} AgentResponse
 * @property {string} text - The raw text response from the agent.
 * @property {string} cleanedText - The text response cleaned for TTS.
 * @property {Buffer | null} audioBuffer - Buffer containing synthesized speech, or null.
 * @property {string | null} audioUrl - URL for the audio, if applicable.
 * @property {number} memorySize - The number of messages in the conversation memory.
 */

class Agent {
  /**
   * @param {AgentConfig} config - Configuration for the Agent.
   */
  constructor({ persona, llmClient, memory, agentGenerationConfig }) {
    if (!persona) throw new Error('Agent requires a Persona instance.');
    if (!llmClient) throw new Error('Agent requires an LLMClient instance.');
    if (!memory) throw new Error('Agent requires a Memory instance.');

    this.persona = persona;
    this.llmClient = llmClient;
    this.memory = memory;
    this.agentGenerationConfig = agentGenerationConfig || llmClient.defaultGenerationConfig;
    this.tools = {}; // Initialize tools
    this.modelName = this.persona.identity.base_model || 'gemini-2.0-flash-lite'; // Aligned with specific successor model
  }

  /**
   * Adds a tool to the agent.
   * @param {string} name - The name of the tool.
   * @param {object} toolInstance - The instance of the tool.
   */
  addTool(name, toolInstance) {
    this.tools[name] = toolInstance;
    console.log(`Tool '${name}' added to Agent '${this.persona.name}'.`);
  }

  /**
   * Processes a user's message and returns the agent's response.
   * @param {string} message - The user's message.
   * @param {object} [context={}] - Additional context for processing.
   * @param {string} [context.sessionId] - The session ID for memory.
   * @param {boolean} [context.generateSpeech=false] - Whether to generate speech.
   * @param {string} [context.audioUrl] - URL to associate with generated audio.
   * @param {boolean} [context.getMemoryOnly=false] - If true, only returns memory content.
   * @param {boolean} [context.clearMemory=false] - If true, clears memory before processing.
   * @returns {Promise<AgentResponse | object>} The agent's response or memory data.
   */
  async processMessage(message, context = {}) {
    try {
      console.log(`Agent [${this.persona.name}] processing message: "${message}" for session [${context.sessionId || this.memory.sessionId}]`);

      if (context.sessionId && context.sessionId !== this.memory.sessionId) {
        await this.memory.setSessionId(context.sessionId);
      }

      if (context.clearMemory) {
        await this.memory.clear();
        return { success: true, message: `Memory cleared for agent ${this.persona.name}` };
      }

      if (context.getMemoryOnly) {
        return {
          messages: this.memory.getAllMessages(),
          count: this.memory.messages.length,
          sessionId: this.memory.sessionId,
          agentName: this.persona.name
        };
      }

      if (message === '__welcome__') {
        const welcomeText = `Good day. I am ${this.persona.identity.agent_name || this.persona.name}, at your service. How may I assist you today?`;
        const cleanedWelcomeText = cleanTextForTTS(welcomeText); // Clean for consistency
        let audioBuffer = null;
        if (context.generateSpeech) {
          audioBuffer = await synthesizeSpeech(cleanedWelcomeText, this.persona.getVoiceSettings());
        }
        return {
          text: welcomeText,
          cleanedText: cleanedWelcomeText,
          audioBuffer,
          audioUrl: context.audioUrl || null,
          memorySize: this.memory.messages.length,
        };
      }

      const systemPrompt = this.persona.generateSystemPrompt();
      const recentConversationHistory = await this.memory.getFormattedHistoryForModel(); // Short-term memory

      // --- RAG: Retrieval Step ---
      let retrievedContextText = "";
      try {
        if (message !== '__welcome__') { // Don't retrieve for welcome message
          console.log(`Agent: Generating embedding for user query: "${message.substring(0, 50)}..."`);
          const queryEmbedding = await generateEmbedding(message);
          console.log(`Agent: Querying Pinecone for session ${context.sessionId} with topK: 3`);
          const pineconeResults = await queryFromPinecone(context.sessionId, queryEmbedding, 3); // topK = 3

          if (pineconeResults && pineconeResults.length > 0) {
            retrievedContextText = "Relevant information from past conversation:\n";
            pineconeResults.forEach(match => {
              if (match.metadata && match.metadata.originalText) {
                const role = match.metadata.role || 'unknown';
                retrievedContextText += `- ${role}: ${match.metadata.originalText}\n`;
              }
            });
            retrievedContextText += "---\n";
            console.log(`Agent: Retrieved ${pineconeResults.length} relevant snippets from Pinecone.`);
          } else {
            console.log("Agent: No relevant snippets found in Pinecone for this query.");
          }
        }
      } catch (ragError) {
        console.error("Agent: Error during RAG retrieval step:", ragError);
        // Continue without RAG context if it fails
      }
      // --- End RAG: Retrieval Step ---

      let fullHistory = [
        { role: "user", parts: [{ text: systemPrompt }] },
        { role: "model", parts: [{ text: `I understand my role as ${this.persona.identity.agent_name || this.persona.name}. I will respond according to my persona.` }] }
      ];

      // Add retrieved RAG context if available
      if (retrievedContextText) {
        fullHistory.push({ role: "user", parts: [{ text: retrievedContextText }] });
        // Optional: Add a model part to acknowledge the context, or let the main prompt flow.
        // fullHistory.push({ role: "model", parts: [{ text: "Understood. I will consider this information." }] });
      }

      fullHistory = fullHistory.concat(recentConversationHistory);
      fullHistory.push({ role: "user", parts: [{ text: message }] });

      // --- Tool Call Integration START ---
      // Tool calls might also benefit from RAG context, but handleToolCalls currently doesn't use fullHistory.
      // For now, tool call decision is based on userQuery and its own system prompt.
      const toolCallResultObject = await this.handleToolCalls(message, context.sessionId, context);
      const textualToolResponse = toolCallResultObject.text;
      const imageUrlsFromTool = toolCallResultObject.imageUrls;

      if (textualToolResponse && !textualToolResponse.startsWith("Error:")) {
        // Add user message and tool response to short-term memory
        await this.memory.addMessage({ role: 'user', text: message }, context.sessionId);
        await this.memory.addMessage({ role: 'assistant', text: textualToolResponse }, context.sessionId);

        // --- RAG: Ingestion Step (Tool Response) ---
        // Asynchronously add to Pinecone, don't await to avoid blocking response
        Promise.all([
          this._ingestToPinecone(context.sessionId, 'user', message),
          this._ingestToPinecone(context.sessionId, 'assistant', textualToolResponse)
        ]).catch(ingestError => console.error("Agent: Error during async ingestion (tool response):", ingestError));
        // --- End RAG: Ingestion Step ---

        const cleanedToolResponse = cleanTextForTTS(textualToolResponse);
        let audioBuffer = null;
        if (context.generateSpeech) {
          audioBuffer = await synthesizeSpeech(cleanedToolResponse, this.persona.getVoiceSettings());
        }
        return {
          text: textualToolResponse,
          cleanedText: cleanedToolResponse,
          audioBuffer,
          audioUrl: context.audioUrl || null,
          imageUrls: imageUrlsFromTool,
          memorySize: this.memory.messages.length,
        };
      } else if (textualToolResponse && textualToolResponse.startsWith("Error:")) {
        // ... (error handling for tool call, no RAG ingestion for errors)
        console.error(`Error during tool call processing for agent ${this.persona.name}: ${textualToolResponse}`);
        const cleanedErrorResponse = cleanTextForTTS(textualToolResponse);
        let audioBuffer = null;
        if (context.generateSpeech && !textualToolResponse.toLowerCase().includes("unknown command")) {
          audioBuffer = await synthesizeSpeech(cleanedErrorResponse, this.persona.getVoiceSettings());
        }
        return {
          text: textualToolResponse,
          cleanedText: cleanedErrorResponse,
          audioBuffer,
          audioUrl: context.audioUrl || null,
          imageUrls: imageUrlsFromTool,
          memorySize: this.memory.messages.length,
          error: true
        };
      }
      // --- Tool Call Integration END ---
      // If no tool was called (textualToolResponse is null), proceed with normal LLM call.

      const modelName = this.persona.identity.base_model || 'gemini-2.0-flash-lite';

      console.log(`Agent: Calling LLM ${modelName} with full history (including RAG if any). History length: ${fullHistory.length} parts.`);
      let reply = '';
      const stream = this.llmClient.generateContentStream({
        modelName,
        history: fullHistory,
        generationConfig: this.agentGenerationConfig,
      });

      for await (const chunk of stream) {
        reply += chunk.text;
      }

      console.log(`Agent [${this.persona.name}] full streamed reply:`, reply);

      reply = getRandomFollowUpPhrase ? replaceFollowUpPhrases(reply, { previousTopic: message }) : reply;
      const cleanedReply = cleanTextForTTS(reply);

      // Add to short-term memory
      await this.memory.addMessage({ role: 'user', text: message }, context.sessionId);
      await this.memory.addMessage({ role: 'assistant', text: reply }, context.sessionId);

      // --- RAG: Ingestion Step (Normal Reply) ---
      // Asynchronously add to Pinecone, don't await to avoid blocking response
      Promise.all([
        this._ingestToPinecone(context.sessionId, 'user', message),
        this._ingestToPinecone(context.sessionId, 'assistant', reply)
      ]).catch(ingestError => console.error("Agent: Error during async ingestion (normal reply):", ingestError));
      // --- End RAG: Ingestion Step ---

      let audioBuffer = null;
      if (context.generateSpeech) {
        audioBuffer = await synthesizeSpeech(cleanedReply, this.persona.getVoiceSettings());
      }

      return {
        text: reply,
        cleanedText: cleanedReply,
        audioBuffer,
        audioUrl: context.audioUrl || null,
        memorySize: this.memory.messages.length,
      };

    } catch (error) {
      console.error(`Error in Agent [${this.persona.name}] processMessage:`, error);

      const errorText = "I'm sorry, I encountered an issue while processing your request. Please try again.";
      const cleanedErrorText = cleanTextForTTS(errorText);

      // Try to generate TTS for error message
      let audioBuffer = null;
      if (context.generateSpeech) {
        try {
          audioBuffer = await synthesizeSpeech(cleanedErrorText, this.persona.getVoiceSettings());
        } catch (ttsError) {
          console.warn(`Failed to generate TTS for error message:`, ttsError.message);
        }
      }

      // Ensure a consistent error response structure
      return {
        text: errorText,
        cleanedText: cleanedErrorText,
        audioBuffer,
        audioUrl: null,
        memorySize: this.memory.messages ? this.memory.messages.length : 0, // Handle if memory is undefined
        error: true
      };
    }
  }

  async _ingestToPinecone(sessionId, role, text) {
    if (!text || text.trim() === "") {
      console.log("Agent [_ingestToPinecone]: Skipping ingestion for empty text.");
      return;
    }
    try {
      console.log(`Agent [_ingestToPinecone]: Generating embedding for ${role} message (session: ${sessionId}). Text: "${text.substring(0,50)}..."`);
      const embedding = await generateEmbedding(text);
      const messageId = crypto.randomUUID(); // Generate a unique ID for the Pinecone record
      const metadata = {
        role: role,
        timestamp: new Date().toISOString(),
        originalText: text,
        // sessionId is already added by upsertToPinecone wrapper, but good to be explicit if needed elsewhere
      };
      await upsertToPinecone(sessionId, messageId, embedding, metadata);
      console.log(`Agent [_ingestToPinecone]: Successfully ingested ${role} message ${messageId} to Pinecone for session ${sessionId}.`);
    } catch (error) {
      console.error(`Agent [_ingestToPinecone]: Failed to ingest ${role} message to Pinecone for session ${sessionId}. Error:`, error);
      // Decide if this error should be propagated or just logged. For async, logging is often sufficient.
    }
  }

  getVoiceSettings() {
    return this.persona.getVoiceSettings();
  }

  async clearMemory() {
    await this.memory.clear();
    console.log(`Memory cleared for agent ${this.persona.name}`);
  }

  async _generateLLMResponseForTools(currentPromptContent, systemInstructionString = null) {
    try {
      const historyForLLM = [];
      if (systemInstructionString) {
        historyForLLM.push({ role: 'user', parts: [{ text: `${systemInstructionString}\n\nUser Query: ${currentPromptContent}` }] });
      } else {
        historyForLLM.push({ role: 'user', parts: [{ text: currentPromptContent }] });
      }
      const toolCallSpecificGenConfig = { 
        ...(this.agentGenerationConfig || {}),
        maxOutputTokens: (this.agentGenerationConfig?.maxOutputTokens || 4096) < 1024 ? 1024 : (this.agentGenerationConfig?.maxOutputTokens || 4096),
        temperature: 0.2
      };
      const responseText = await this.llmClient.generateContent({
        modelName: this.modelName,
        history: historyForLLM,
        generationConfig: toolCallSpecificGenConfig 
      });
      return responseText;
    } catch (error) {
      console.error(`Error in Agent._generateLLMResponseForTools: ${error.message}`);
      return `Error: LLM response generation failed for tool logic. ${error.message}`;
    }
  }

  async handleToolCalls(userQuery, sessionId, context = {}) {
    const nluAnalysis = context.nluAnalysis || null;
    const imageDataUrls = context.imageDataUrls || null; 

    const llmToolCallDecision = await this._generateLLMResponseForTools(userQuery, this.getToolCallSystemPrompt());
    const processedText = llmToolCallDecision ? llmToolCallDecision.trim() : "";

    if (processedText.startsWith('TOOL_CALL:')) {
      try {
        const callInfo = this.parseToolCall(processedText);
        if (!callInfo) {
          console.error(`Agent [${this.persona.name}] Error: Invalid tool call format from LLM: "${processedText}"`);
          return "Error: Invalid tool call format from LLM.";
        }

        const { toolName, command, args: parsedArgsArray } = callInfo;
        const toolInstance = this.tools[toolName];

        if (!toolInstance) {
          console.error(`Agent [${this.persona.name}] Error: Tool '${toolName}' is not available.`);
          return { text: `Error: Tool '${toolName}' is not available.`, imageUrls: undefined };
        }

        // Validate command for object-based tools; imgGenTool, documentTool, and gmail are handled differently.
        // gmail uses an 'action' parameter within its execute method.
        if (toolName !== 'imgGenTool' && toolName !== 'documentTool' && toolName !== 'gmail' && toolName !== 'videoTool' && typeof toolInstance[command] !== 'function') { // Added videoTool here
            console.error(`Agent [${this.persona.name}] Error: Tool '${toolName}' does not have command '${command}'.`);
            return { text: `Error: Tool '${toolName}' does not have command '${command}'.`, imageUrls: undefined };
        }
        
        let toolResult;
        let finalToolCallArgs; // This variable is used by some other tools
        let imageUrlsForResponse = []; // Initialize here

        if (toolName === 'gmail') {
            const action = command; // 'command' from parseToolCall is the action like 'list_messages'
            const emailToolArgs = { action };

            if (action !== 'list_labels' && action !== 'list_messages') {
                console.error(`Agent [${this.persona.name}] Error: Invalid action '${action}' for gmail tool.`);
                return { text: `Error: Invalid action '${action}' for gmail tool. Valid actions are 'list_labels' or 'list_messages'.`, imageUrls: undefined };
            }

            if (parsedArgsArray.length > 0) {
                emailToolArgs.query = parsedArgsArray[0];
            }
            if (parsedArgsArray.length > 1) {
                const maxRes = parseInt(parsedArgsArray[1], 10);
                if (!isNaN(maxRes)) {
                    emailToolArgs.maxResults = maxRes;
                } else {
                    console.warn(`Agent [${this.persona.name}] Warning: Invalid maxResults '${parsedArgsArray[1]}' for gmail tool. Using default.`);
                    // If maxResults is not a valid number, it will be undefined, and emailTool.execute will use its default.
                }
            }
            
            console.log(`Agent [${this.persona.name}] Calling gmail tool with args: ${JSON.stringify(emailToolArgs)}`);
            toolResult = await toolInstance.execute(emailToolArgs);

        } else if (toolName === 'imgGenTool' && command === 'generate') {
            const prompt = parsedArgsArray[0] ? parsedArgsArray[0].trim() : "";
            if (!prompt) {
                console.error(`Agent [${this.persona.name}] Error: Empty prompt received for imgGenTool 'generate' command.`);
                return { text: "Error: To generate an image, please provide a text description for the image.", imageUrls: undefined };
            }
            let options = {};
            if (parsedArgsArray.length > 1) {
                try {
                    // LLM might output single quotes for JSON string, replace them for robust parsing
                    const optionsString = parsedArgsArray[1].replace(/'/g, '"');
                    options = JSON.parse(optionsString);
                } catch (e) {
                    console.warn(`Agent [${this.persona.name}] Warning: Could not parse options for imgGenTool: ${parsedArgsArray[1]}. Error: ${e.message}. Using default options.`);
                }
            }
            const imgGenParamsStr = JSON.stringify({ action: command, query: prompt, options });
            toolResult = await toolInstance(imgGenParamsStr); // toolInstance is the imgGenTool function

            if (typeof toolResult === 'string' && toolResult.startsWith('Generated')) {
                const lines = toolResult.split('\n');
                if (lines.length > 1) {
                    for (let i = 1; i < lines.length; i++) {
                        const filePath = lines[i].trim();
                        if (filePath) {
                            const baseName = path.basename(filePath.replace(/\\/g, '/')); // Normalize path separators
                            imageUrlsForResponse.push(`/generated-images/${baseName}`);
                        }
                    }
                }
                // The textual toolResult for summarization should be the original string from imgGenTool
            }
            // toolResult (string) is passed to generateSummarizedResponse
            // imageUrlsForResponse is now populated if images were generated
        } else if ((toolName === 'describeImage' || toolName === 'calculatorTool' || toolName === 'browserTool') && command === 'execute') {
          const prefix = 'TOOL_CALL:';
          const callStringRaw = processedText.substring(prefix.length).trim();
          const toolNameAndCommandPrefix = `${toolName}:${command}`;
          let actualJsonArgString = "";

          if (callStringRaw.startsWith(toolNameAndCommandPrefix)) {
            actualJsonArgString = callStringRaw.substring(toolNameAndCommandPrefix.length).trim();
          } else {
             console.error(`Agent [${this.persona.name}] Error: Unexpected format for ${toolName} call. Expected prefix '${toolNameAndCommandPrefix}', got '${callStringRaw}'`);
             throw new Error(`Unexpected format for ${toolName} tool call string.`);
          }
          let toolJsonArgsString = actualJsonArgString.replace(/^'|'$/g, '');

          try {
            let parsedToolArgs = JSON.parse(toolJsonArgsString);
            if (toolName === 'describeImage' && parsedToolArgs.sources === "imageDataFromContext") {
              if (imageDataUrls && Array.isArray(imageDataUrls) && imageDataUrls.length > 0) {
                if (imageDataUrls.length === 1) {
                  parsedToolArgs.sources = imageDataUrls[0];
                  console.log(`Agent: Replaced "imageDataFromContext" with single actual Data URL for describeImage tool.`);
                } else {
                  parsedToolArgs.sources = imageDataUrls;
                  console.log(`Agent: Replaced "imageDataFromContext" with an array of ${imageDataUrls.length} Data URLs for describeImage tool.`);
                }
              } else {
                throw new Error("imageDataFromContext placeholder used by LLM, but no imageDataUrls (or empty array) found in agent context.");
              }
              // describeImage tool's execute method might expect a stringified JSON if it does its own parsing,
              // or an object if it expects pre-parsed args. Assuming it expects a string for now based on existing logic.
              // If it expects an object, this should be JSON.parse(toolJsonArgsString) or parsedToolArgs directly.
              finalToolCallArgs = [JSON.stringify(parsedToolArgs)]; // Keep as string for describeImage
            } else {
              // For calculatorTool, browserTool or describeImage not using imageDataFromContext
              finalToolCallArgs = [toolJsonArgsString]; // Keep as string initially, parse for specific tools below
            }
          } catch (parseError) {
            console.error(`Agent: Error parsing/modifying JSON arguments for ${toolName}: ${parseError}. Original reconstructed arg: ${actualJsonArgString}`);
            throw new Error(`Invalid JSON argument structure for ${toolName} tool: ${parseError.message}`);
          }

          // For calculatorTool and browserTool, the execute method expects an object.
          // So we parse the JSON string and pass the resulting object.
          if (toolName === 'calculatorTool' || toolName === 'browserTool') {
            toolResult = await toolInstance[command](JSON.parse(finalToolCallArgs[0]));
            // --- NEW: Remove base64Screenshot for browserTool to prevent token overflow ---
            if (toolName === 'browserTool' && toolResult && typeof toolResult === 'object' && toolResult.base64Screenshot) {
                delete toolResult.base64Screenshot;
                console.log(`Agent [${this.persona.name}] Removed base64Screenshot from browserTool result before summarization.`);
            }
            // --- END NEW ---
          } else { // For describeImage (which might expect a stringified JSON)
            toolResult = await toolInstance[command](finalToolCallArgs[0]);
          }
        } else if (toolName === 'search' && command === 'execute' && parsedArgsArray.length === 1) {
          finalToolCallArgs = [parsedArgsArray[0], nluAnalysis];
          toolResult = await toolInstance[command](...finalToolCallArgs);
        } else if (toolName === 'KnowledgeBaseTool') {
            let kbParams = {};
            if (command === 'get') {
                if (parsedArgsArray.length >= 2) {
                    kbParams.type = parsedArgsArray[0];
                    kbParams.name = parsedArgsArray[1];
                } else {
                    throw new Error(`KnowledgeBaseTool 'get' command requires 'type' and 'name' arguments.`);
                }
            } else if (command === 'list') {
                if (parsedArgsArray.length >= 1) {
                    kbParams.type = parsedArgsArray[0]; // Optional type
                }
                // If no args, kbParams remains empty, which is fine for list all.
            } else if (command === 'search') {
                if (parsedArgsArray.length >= 1) {
                    kbParams.keywords = parsedArgsArray[0];
                    if (parsedArgsArray.length >= 2) {
                        kbParams.type = parsedArgsArray[1]; // Optional type
                    }
                } else {
                    throw new Error(`KnowledgeBaseTool 'search' command requires 'keywords' argument.`);
                }
            } else if (command === 'add') {
                if (parsedArgsArray.length >= 3) {
                    kbParams.type = parsedArgsArray[0];
                    kbParams.name = parsedArgsArray[1];
                    kbParams.content = parsedArgsArray[2];
                    if (parsedArgsArray.length >= 4) {
                        try {
                            kbParams.metadata = JSON.parse(parsedArgsArray[3]);
                        } catch (e) {
                            throw new Error(`KnowledgeBaseTool 'add' command metadata argument is not valid JSON: ${parsedArgsArray[3]}`);
                        }
                    }
                } else {
                    throw new Error(`KnowledgeBaseTool 'add' command requires 'type', 'name', and 'content' arguments.`);
                }
            } else {
                throw new Error(`Unknown command '${command}' for KnowledgeBaseTool.`);
            }
            toolResult = await toolInstance[command](kbParams);
        } else if (toolName === 'documentTool') {
            // documentTool expects a single string: action "path" ["secondPathOrOptions"] ["optionsIfSecondPath"]
            let toolInputString = command; // e.g., "analyze"
            if (parsedArgsArray.length > 0) {
                toolInputString += ` "${parsedArgsArray[0]}"`; // path
            }
            if (parsedArgsArray.length > 1) {
                // Second arg could be another path (for compare) or a JSON options string or a prompt string
                toolInputString += ` "${parsedArgsArray[1]}"`;
            }
            if (parsedArgsArray.length > 2) {
                // Third arg (if present) would be options for compare, or if prompt was separate for others
                 toolInputString += ` "${parsedArgsArray[2]}"`;
            }
            // The documentTool itself handles parsing this string.
            toolResult = await toolInstance(toolInputString); // toolInstance is documentTool function
            // imageUrlsForResponse will likely be undefined for documentTool
        } else if (toolName === 'videoTool') { // Added videoTool specific handling
            // videoTool expects a single string argument: "action videoPath [options]"
            // The 'command' from parseToolCall is the action (e.g., 'analyze', 'summarize')
            // The 'parsedArgsArray' contains the path and any options

            let toolInputString = command; // e.g., "analyze"

            if (parsedArgsArray.length > 0) {
                // The first arg is typically the videoPath
                toolInputString += ` "${parsedArgsArray[0]}"`;
            }

            // Append any remaining arguments as options string
            if (parsedArgsArray.length > 1) {
                // Reconstruct the options string from the rest of the arguments
                // This assumes options are passed as "key:value" pairs or a JSON string after the path
                const remainingArgs = parsedArgsArray.slice(1).map(arg => {
                    // If it's a JSON string that was originally quoted, it needs to remain so.
                    // This is a heuristic and might need fine-tuning depending on how LLM outputs options.
                    if (arg.startsWith('{') && arg.endsWith('}')) {
                        return `'${arg}'`; // Re-quote JSON strings for videoTool's parser
                    }
                    return arg; // For simple key:value pairs
                }).join(' ');

                if (remainingArgs) {
                    toolInputString += ` ${remainingArgs}`;
                }
            }

            console.log(`Agent [${this.persona.name}] Calling videoTool with string: "${toolInputString}"`);
            toolResult = await toolInstance(toolInputString); // toolInstance is the videoTool function itself
            // imageUrlsForResponse will likely be undefined for videoTool
        }
        else { // Generic handling for other tools that expect direct command calls
          finalToolCallArgs = parsedArgsArray;
          toolResult = await toolInstance[command](...finalToolCallArgs);
        }
        
        const summarizedText = await this.generateSummarizedResponse(userQuery, toolResult, toolName, nluAnalysis);
        return { text: summarizedText, imageUrls: imageUrlsForResponse.length > 0 ? imageUrlsForResponse : undefined };

      } catch (error) {
        console.error(`Agent [${this.persona.name}] Error: Tool call processing failed. ${error.message}`);
        return { text: `Error: Tool call processing failed. ${error.message}`, imageUrls: undefined };
      }
    } else if (processedText === "NO_TOOL" || processedText.startsWith("Error:")) {
      // If no tool is called, or an error occurred in deciding to call a tool
      return { text: null, imageUrls: undefined };
    } else {
      // If LLM responds with something other than TOOL_CALL or NO_TOOL (should not happen with current prompt)
      console.warn(`Agent [${this.persona.name}] Warning: LLM tool call decision was not TOOL_CALL or NO_TOOL: "${processedText}"`);
      return { text: null, imageUrls: undefined };
    }
  }

  parseToolCall(text) {
    const prefix = 'TOOL_CALL:';
    if (!text.startsWith(prefix)) {
      return null;
    }
    const callString = text.substring(prefix.length).trim();
    const toolNameEndIndex = callString.indexOf(':');
    if (toolNameEndIndex === -1) {
      return null;
    }
    const toolName = callString.substring(0, toolNameEndIndex).trim();
    const commandAndArgsString = callString.substring(toolNameEndIndex + 1).trim();
    
    const commandParts = commandAndArgsString.match(/(?:[^\s"]+|"[^"]*")+/g) || [];
    if (commandParts.length === 0) {
        return null;
    }
    const command = commandParts[0];
    const args = commandParts.slice(1).map(arg => arg.replace(/^"|"$/g, ''));

    return { toolName, command, args };
  }

  /**
   * Generates a summarized response from LLM after a tool call.
   * This method is called after a tool has executed and returned its raw result.
   * @param {string} userQuery - The original user query.
   * @param {any} rawToolResult - The raw output from the tool.
   * @param {string} toolName - The name of the tool that was called.
   * @param {object} [nluAnalysis=null] - Optional NLU analysis from the user query.
   * @returns {Promise<string>} The summarized text response from the LLM.
   */
  async generateSummarizedResponse(userQuery, rawToolResult, toolName, nluAnalysis = null) {
    if (typeof rawToolResult === 'string' && rawToolResult.startsWith("Error:")) {
        return rawToolResult;
    }
    let formattedToolResult = rawToolResult; // This line already exists

    // --- START OF TOKEN LIMITATION LOGIC ---
    // This block should be placed right after the `formattedToolResult`
    // is initialized, and before any console.log statements that print
    // the full formattedToolResult to the console (unless you want to
    // see the full, untruncated version in logs for debugging).

    // First, ensure formattedToolResult is a string before checking length
    // This handles cases where toolResult is an object (like from browserTool's screenshot)
    if (typeof formattedToolResult !== 'string') {
        try {
            formattedToolResult = JSON.stringify(formattedToolResult, null, 2); // Stringify the object
        } catch (e) {
            console.error(`Agent [${this.persona.name}] Failed to stringify toolResult for truncation:`, e);
            formattedToolResult = `Error: Could not format tool result for summarization. Original type: ${typeof rawToolResult}`;
        }
    }

    // Define a maximum character limit for the tool result.
    // This is a heuristic. Gemini models are roughly 4 characters per token.
    // The Gemini 2.0 Flash-Lite model has a max input of 1,048,576 tokens.
    // We need to leave room for the system prompt, conversation history, and the user query.
    // Let's estimate that the prompt and history might take up to 200,000 tokens (800,000 chars).
    // So, we can allocate roughly 800,000 tokens (3,200,000 chars) for the tool result.
    // Adjust these numbers based on your actual prompt/history sizes and observed token usage.
    const MAX_TOOL_RESULT_CHARS = 3000000; // Roughly 750,000 tokens. Adjust as needed.

    if (formattedToolResult.length > MAX_TOOL_RESULT_CHARS) {
        console.warn(`Agent [${this.persona.name}] Tool result from ${toolName} is very large (${formattedToolResult.length} characters). Truncating to ${MAX_TOOL_RESULT_CHARS} characters to prevent token overflow.`);

        // Truncate the text
        formattedToolResult = formattedToolResult.substring(0, MAX_TOOL_RESULT_CHARS);

        // Add a clear indicator that content was truncated
        formattedToolResult += "\n\n--- Content truncated due to length. Please ask more specific questions if you need information from later parts. ---";

        // Optional: For more intelligent summarization instead of simple truncation,
        // you would make another LLM call here, sending *only* the large toolResult
        // to a dedicated summarization prompt. This adds latency and cost.
        // (See previous response for commented-out example of this more advanced approach)
    }
    // --- END OF TOKEN LIMITATION LOGIC ---


    let nluContextString = "";
    // ... rest of your existing generateSummarizedResponse method ...
    // The rest of the function remains the same, using the potentially modified `formattedToolResult`

    if (nluAnalysis) {
        nluContextString = "\n\nUser's NLU Context:\n";
        if (nluAnalysis.intent) {
            nluContextString += `- Detected Intent: ${nluAnalysis.intent}\n`;
        }
        if (nluAnalysis.entities && Object.keys(nluAnalysis.entities).length > 0) {
            nluContextString += "- Detected Entities:\n";
            for (const [key, value] of Object.entries(nluAnalysis.entities)) {
                nluContextString += `  - ${key}: ${Array.isArray(value) ? value.join(', ') : value}\n`;
            }
        }
        if (nluContextString === "\n\nUser's NLU Context:\n") nluContextString = "";
    }

    // The following blocks (KnowledgeBaseTool, search) are for specific formatting
    // of tool results *before* the general stringify/truncate.
    // If the toolResult is already a string, these might re-process it.
    // It's generally better to apply these specific formatters *before* the
    // general `if (typeof formattedToolResult !== 'string') { JSON.stringify(...) }`
    // block, or ensure they also handle large outputs.
    // For now, I'm keeping them in their original position as per your provided code,
    // but be aware of the order of operations.
    if (toolName === 'KnowledgeBaseTool' && Array.isArray(rawToolResult)) { // Use rawToolResult here
        if (rawToolResult.length === 0) {
            formattedToolResult = "No relevant information was found in the knowledge base for your query.";
        } else {
            formattedToolResult = "Knowledge Base Information:\n" + rawToolResult.map((res, index) => {
                let itemContext = res.context || (res.fullContent ? (res.fullContent.substring(0, 250) + (res.fullContent.length > 250 ? "..." : "")) : 'No content preview.');
                let metadataInfo = "";
                if (res.metadata) {
                    metadataInfo = ` (Description: ${res.metadata.description || 'N/A'}, Tags: ${res.metadata.tags ? res.metadata.tags.join(', ') : 'N/A'})`;
                }
                return `${index + 1}. Entry: ${res.name} (Type: ${res.type}, Path: ${res.relativePath})${metadataInfo}\n   Context: ${itemContext}`;
            }).join('\n\n');
        }
    } else if (toolName === 'search' && Array.isArray(rawToolResult)) { // Existing web search handling, use rawToolResult
      if (rawToolResult.length === 0 || (rawToolResult.length === 1 && rawToolResult[0].title === 'Search Error')) {
        formattedToolResult = "No relevant search results were found or an error occurred during the search.";
      } else {
        formattedToolResult = "Search Results:\n" + rawToolResult.map((res, index) => {
          let preview = res.content || ''; // Assumes web search result structure
          if (preview.length > 250) {
            preview = preview.substring(0, 250) + "...";
          }
          return `${index + 1}. Title: ${res.title}\n   URL: ${res.url}\n   Snippet: ${res.snippet || 'N/A'}\n   Content Preview: ${preview || 'N/A'}`;
        }).join('\n\n');
      }
    }
    // Removed the redundant `else if (typeof toolResult !== 'string') { formattedToolResult = JSON.stringify(toolResult); }`
    // because the new truncation logic handles all non-string `toolResult` by stringifying them first.


    let taskInstruction = `Your task is to use this data (and the NLU context if provided) to provide a direct, conversational answer to the user's original query.`;
    if (toolName === 'search' || toolName === 'KnowledgeBaseTool') { // Apply detailed instruction for KBTool as well
      taskInstruction = `Your task is to use this data (and the NLU context if provided) to provide a comprehensive and elaborative conversational answer to the user's original query. Discuss key details, offer context found in the results, and synthesize the information thoughtfully. Aim to be thorough yet engaging, in line with your persona.`;
    }

    let gmailSpecificInstructions = "";
    if (toolName === 'gmail') {
        gmailSpecificInstructions = `
When summarizing the emails, pay close attention to the 'nluAnalysis' field for each email object within the provided data. This 'nluAnalysis' field contains insights like sentiment, intent, and key entities for that specific email. Use these NLU details to:
- Understand the core message and purpose of each email.
- Capture the tone or sentiment if it's noteworthy (e.g., if an email is urgent or expresses strong emotion).
- Highlight any important entities (people, organizations, locations, dates) or actions mentioned.
Incorporate these NLU-driven insights into your summary to make it more informative and reflective of the emails' content.`;
    }

    const summarizationSystemPrompt = `You are VARJIS., a helpful AI assistant. A tool was used to get data relevant to the user's query.
The user's original query was: "${userQuery}".${nluContextString}
The data obtained from the tool ("${toolName}") is:
---
${formattedToolResult}
---
${taskInstruction}
${gmailSpecificInstructions}
Focus on relevance to the user's intent and entities.
Do not explicitly mention that you used a tool or how you got the data, unless the query was specifically about your tools or methods. Just answer the question naturally using the provided data. If the data indicates no results were found or an error occurred, inform the user politely.`;

    const summarizationPrompt = `Based on the data provided (and NLU context if available), provide a conversational answer to the user's query: "${userQuery}".`;

    console.log(`Agent [${this.persona.name}] generateSummarizedResponse - User Query: ${userQuery}`);
    console.log(`Agent [${this.persona.name}] generateSummarizedResponse - Tool Name: ${toolName}`);
    // console.log(`Agent [${this.persona.name}] generateSummarizedResponse - Raw Tool Result:`, JSON.stringify(rawToolResult, null, 2)); // Can be very verbose
    console.log(`Agent [${this.persona.name}] generateSummarizedResponse - Formatted Tool Result for LLM:\n---\n${formattedToolResult}\n---`);
    // console.log(`Agent [${this.persona.name}] generateSummarizedResponse - Summarization System Prompt for LLM:\n---\n${summarizationSystemPrompt}\n---`); // Can be very verbose

    const llmSummaryResponse = await this._generateLLMResponseForTools(summarizationPrompt, summarizationSystemPrompt);
    console.log(`Agent [${this.persona.name}] generateSummarizedResponse - LLM Summary Response: ${llmSummaryResponse}`);
    return llmSummaryResponse;
  }

  // Moved getToolCallSystemPrompt inside the class
  getToolCallSystemPrompt() {
    let toolDocs = Object.entries(this.tools)
      .map(([name, tool]) => {
        let docString = `No specific documentation available for ${name}.`;
        if (tool && typeof tool.getCommandsDocumentation === 'function') {
          docString = tool.getCommandsDocumentation();
        } else if (tool && tool.description) {
          docString = tool.description; 
        }
        return `Tool Name: ${name}\nDescription and Commands:\n${docString}`;
      })
      .join('\n\n---\n\n');

    if (!toolDocs) {
        toolDocs = "No tools currently available.";
    }

    return `You are an AI assistant with access to external tools.
Your task is to determine if the user's query requires using one of these tools.

Available tools:
---
${toolDocs}
---

IMPORTANT: If you determine a tool is needed, your response MUST be ONLY the tool call string, formatted EXACTLY as shown below. Do NOT add any other text, explanation, or conversational filler.

Format for tool calls:
TOOL_CALL: <tool_name>:<command_name> <arg1_value> <arg2_value> ...

Examples of when to call a tool (follow this exact format):
- User: "What time is it in London?" -> TOOL_CALL: datetime_tool: now Europe/London
- User: "Format today as YYYY-MM-DD" -> TOOL_CALL: datetime_tool: format now "yyyy-MM-dd"
- User: "What will the date be in 7 days?" -> TOOL_CALL: datetime_tool: add now 7 days
- User: "Describe the image I uploaded" (and an image was uploaded by the user) -> TOOL_CALL: describeImage:execute '{"sources": "imageDataFromContext", "prompt": "Describe the image I uploaded"}'
- User: "What's in this picture? https://example.com/image.png" -> TOOL_CALL: describeImage:execute '{"sources": "https://example.com/image.png", "prompt": "What is in this picture?"}'
- User: "What is 5 times 12?" -> TOOL_CALL: calculatorTool:execute '{"expression": "5 * 12"}'
- User: "Calculate the square root of 144" -> TOOL_CALL: calculatorTool:execute '{"expression": "sqrt(144)"}'
- User: "Convert 100 USD to EUR" -> TOOL_CALL: calculatorTool:execute '{"expression": "100 USD to EUR"}'
- User: "Get the text from https://example.com" -> TOOL_CALL: browserTool:execute '{"url": "https://example.com", "action": "getVisibleText"}'
- User: "Generate an image of a happy dog" -> TOOL_CALL: imgGenTool:generate "a happy dog"
- User: "Create a picture of a futuristic city with flying cars, widescreen" -> TOOL_CALL: imgGenTool:generate "futuristic city with flying cars" '{"aspectRatio": "16:9"}'
- User: "Show me my unread emails" -> TOOL_CALL: gmail:list_messages "is:unread"
- User: "List my Gmail labels" -> TOOL_CALL: gmail:list_labels
- User: "Get <NAME_EMAIL>" -> TOOL_CALL: gmail:list_messages "from:<EMAIL>"
- User: "Analyze this document at path/to/my_document.pdf and tell me the main topics." -> TOOL_CALL: documentTool:analyze "path/to/my_document.pdf" '{"prompt": "What are the main topics of this document?"}'
- User: "Summarize path/to/report.pdf" -> TOOL_CALL: documentTool:summarize "path/to/report.pdf"
- User: "Extract all email addresses from contract.pdf" -> TOOL_CALL: documentTool:extract "contract.pdf" '{"prompt": "Extract all email addresses."}'
- User: "Compare docA.pdf and docB.pdf to find differences in their conclusions." -> TOOL_CALL: documentTool:compare "docA.pdf" "docB.pdf" '{"prompt": "What are the differences in their conclusions?"}'
- User: "Help me with the document tool" -> TOOL_CALL: documentTool:help
- User: "Tell me about parallelExampleConfig from your knowledge base" -> TOOL_CALL: KnowledgeBaseTool:get "configuration" "parallel_example_config.js"
- User: "Search knowledge base for agent factory examples" -> TOOL_CALL: KnowledgeBaseTool:search "agent factory example" "snippet"

If the query does NOT require a tool, or if you are unsure, respond ONLY with the exact string "NO_TOOL".
Do not add any other explanations or text.`;
  }

  parseToolCall(text) {
    const prefix = 'TOOL_CALL:';
    if (!text.startsWith(prefix)) {
      return null;
    }
    const callString = text.substring(prefix.length).trim();
    const toolNameEndIndex = callString.indexOf(':');
    if (toolNameEndIndex === -1) {
      return null;
    }
    const toolName = callString.substring(0, toolNameEndIndex).trim();
    const commandAndArgsString = callString.substring(toolNameEndIndex + 1).trim();
    
    const commandParts = commandAndArgsString.match(/(?:[^\s"]+|"[^"]*")+/g) || [];
    if (commandParts.length === 0) {
        return null;
    }
    const command = commandParts[0];
    const args = commandParts.slice(1).map(arg => arg.replace(/^"|"$/g, ''));

    return { toolName, command, args };
  }
}

function replaceFollowUpPhrases(text, context = {}) {
  const commonPhrases = [
    /Is there anything else I can help you with today\??/i,
    /Is there anything else I can assist you with\??/i,
  ];
  let modifiedText = text;
  for (const phrase of commonPhrases) {
    if (phrase.test(modifiedText)) {
      const replacement = getRandomFollowUpPhrase('creative', context);
      modifiedText = modifiedText.replace(phrase, replacement);
      break;
    }
  }
  return modifiedText;
}

export default Agent;

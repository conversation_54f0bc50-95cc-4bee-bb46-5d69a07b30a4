import { GoogleGenAI, createUserContent, createPartFromUri } from "@google/genai";
import fs from 'fs/promises';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

// Configure with your API key
const API_KEY = process.env.GEMINI_API_KEY;

/**
 * Video tool for agent framework
 * Allows the agent to analyze and understand videos using Google's Gemini API
 * @param {string} paramsStr - JSON string or command for the video tool
 * @returns {Promise<string>} - Result of the video operation
 */
async function videoTool(paramsStr) {
  try {
    // Parse parameters
    let action, videoPath, options = {};

    if (paramsStr.startsWith('{')) {
      // JSON format
      const params = JSON.parse(paramsStr);
      action = params.action;
      videoPath = params.videoPath;
      options = params;
    } else {
      // Command format: "action videoPath [options]"
      const parts = paramsStr.split(' ');
      action = parts[0];

      if (action === 'help') {
        return getHelpText();
      }

      if (parts.length < 2 && !['help', 'saveTranscript'].includes(action)) {
        return "Error: Missing video path. Format: 'action videoPath [options]'";
      }

      if (parts.length >= 2) {
        videoPath = parts[1].replace(/["']/g, '');
      }

      // Parse options if provided
      if (parts.length > 2) {
        const optionsStr = parts.slice(2).join(' ');
        const optionPairs = optionsStr.match(/(\w+):([\w\d\s\-:.\/]+)/g) || [];

        optionPairs.forEach(pair => {
          const [key, value] = pair.split(':');
          options[key.trim()] = value.trim();
        });
      }
    }

    // Initialize Google Gemini AI
    const ai = new GoogleGenAI({ apiKey: API_KEY });

    switch (action) {
      case "analyze":
        return await analyzeVideo(ai, videoPath, options.prompt || "Analyze this video in detail.");
      case "summarize":
        return await analyzeVideo(ai, videoPath, options.prompt || "Summarize this video concisely.");
      case "transcribe":
        return await analyzeVideo(ai, videoPath, options.prompt || "Transcribe all speech in this video with timestamps.");
      case "describe":
        return await analyzeVideo(ai, videoPath, options.prompt || "Provide detailed visual descriptions of this video with timestamps.");
      case "timestamp":
        if (!options.time) {
          return "Error: Missing timestamp. Format: 'timestamp videoPath time:MM:SS'";
        }
        return await analyzeTimestamp(ai, videoPath, options.time, options.prompt);
      case "quiz":
        return await createQuiz(ai, videoPath, options);
      case "youtube":
        return await analyzeYouTube(ai, videoPath, options.prompt || "Analyze this YouTube video.");
      case "count":
        return await countVideoTokens(videoPath);
      case "saveTranscript":
        if (!options.text) {
          return "Error: Missing transcript text. Format: 'saveTranscript format:txt outputPath:./transcript.txt text:\"Your transcript here\"'";
        }
        return await saveTranscript(options.text, options.format || 'txt', options.outputPath || './transcript.txt', options.metadata || {});
      default:
        return `Unknown action: ${action}. Available actions: analyze, summarize, transcribe, describe, timestamp, quiz, youtube, count, saveTranscript, help`;
    }
  } catch (error) {
    console.error("Error in video tool:", error);
    return `Error processing video request: ${error.message}`;
  }
}

/**
 * Analyze a video file with a custom prompt
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} videoPath - Path to the video file
 * @param {string} prompt - Custom prompt for analysis
 * @returns {Promise<string>} - Analysis result
 */
async function analyzeVideo(ai, videoPath, prompt) {
  try {
    // Check if the path is a YouTube URL
    if (videoPath.includes('youtube.com/') || videoPath.includes('youtu.be/')) {
      return await analyzeYouTube(ai, videoPath, prompt);
    }

    // Check if the file exists
    try {
      await fs.access(videoPath);
    } catch (error) {
      return `Error: Video file not found at path "${videoPath}"`;
    }

    // Get file size to determine upload method
    const stats = await fs.stat(videoPath);
    const fileSize = stats.size;
    const fileSizeMB = fileSize / (1024 * 1024);

    // Get file extension and determine MIME type
    const ext = path.extname(videoPath).toLowerCase();
    const mimeType = getMimeType(ext);

    if (!mimeType) {
      return `Error: Unsupported video format "${ext}". Supported formats: .mp4, .mpeg, .mov, .avi, .flv, .mpg, .webm, .wmv, .3gp`;
    }

    // Add token usage warning for large videos
    const tokenInfo = await estimateVideoTokens(videoPath);
    let tokenWarning = "";
    if (tokenInfo.durationSeconds > 60) {
      tokenWarning = `\n\nNote: This video is ${tokenInfo.durationFormatted} long and will use approximately ${tokenInfo.tokenEstimate.toLocaleString()} tokens when processed by Gemini.`;
    }

    let response;

    // Use File API for larger files (>20MB)
    if (fileSizeMB > 20) {
      // Upload file to Gemini
      const file = await ai.files.upload({
        file: videoPath,
        config: { mimeType }
      });

      // Analyze the video
      response = await ai.models.generateContent({
        model: "gemini-2.0-flash",
        contents: createUserContent([
          createPartFromUri(file.uri, file.mimeType),
          prompt
        ])
      });
    } else {
      // For smaller files, use inline data
      const videoData = await fs.readFile(videoPath, { encoding: 'base64' });

      response = await ai.models.generateContent({
        model: "gemini-2.0-flash",
        contents: [
          {
            inlineData: {
              mimeType,
              data: videoData,
            },
          },
          { text: prompt }
        ]
      });
    }

    return response.text() + tokenWarning;
  } catch (error) {
    console.error("Error analyzing video:", error);
    return `Error analyzing video: ${error.message}`;
  }
}

/**
 * Analyze a specific timestamp in a video
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} videoPath - Path to the video file
 * @param {string} timestamp - Timestamp in MM:SS format
 * @param {string} customPrompt - Optional custom prompt
 * @returns {Promise<string>} - Analysis result
 */
async function analyzeTimestamp(ai, videoPath, timestamp, customPrompt) {
  const prompt = customPrompt || `What is happening at timestamp ${timestamp} in this video?`;
  return await analyzeVideo(ai, videoPath, prompt);
}

/**
 * Create a quiz based on video content
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} videoPath - Path to the video file
 * @param {object} options - Quiz options
 * @returns {Promise<string>} - Generated quiz
 */
async function createQuiz(ai, videoPath, options) {
  const questionCount = options.questions || 5;
  const difficulty = options.difficulty || "medium";

  const prompt = `Create a quiz with ${questionCount} ${difficulty}-difficulty questions based on the information in this video. Include an answer key.`;
  return await analyzeVideo(ai, videoPath, prompt);
}

/**
 * Analyze a YouTube video
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} youtubeUrl - YouTube video URL
 * @param {string} prompt - Custom prompt for analysis
 * @returns {Promise<string>} - Analysis result
 */
async function analyzeYouTube(ai, youtubeUrl, prompt) {
  try {
    // Validate YouTube URL
    if (!youtubeUrl.includes('youtube.com/watch?v=') && !youtubeUrl.includes('youtu.be/')) {
      return `Error: Invalid YouTube URL. Format should be "https://www.youtube.com/watch?v=VIDEO_ID" or "https://youtu.be/VIDEO_ID"`;
    }

    // Use the models.generateContent method directly
    const result = await ai.models.generateContent({
      model: "gemini-2.0-flash-lite",
      contents: [
        { text: prompt },
        {
          fileData: {
            fileUri: youtubeUrl,
          },
        },
      ],
    });

    return result.text;
  } catch (error) {
    console.error("Error analyzing YouTube video:", error);
    return `Error analyzing YouTube video: ${error.message}`;
  }
}

/**
 * Estimate token usage for a video file
 * @param {string} videoPath - Path to the video file
 * @returns {Promise<object>} - Token usage information
 */
async function estimateVideoTokens(videoPath) {
  try {
    // This is a simplified estimation based on video duration
    // In a real implementation, you would use ffmpeg or a similar tool to get accurate duration

    // For demo purposes, we'll use a placeholder duration of 60 seconds
    // In a real implementation, replace this with actual video duration calculation
    const durationSeconds = 60; // Placeholder

    // Calculate token usage based on Gemini's rules (approximately 300 tokens per second)
    const tokenEstimate = durationSeconds * 300;

    // Format duration for display
    const minutes = Math.floor(durationSeconds / 60);
    const seconds = durationSeconds % 60;
    const durationFormatted = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    return {
      durationSeconds,
      durationFormatted,
      tokenEstimate,
      framesPerSecond: 1, // Gemini samples at 1 FPS
      tokensPerFrame: 258,
      tokensPerSecondAudio: 32
    };
  } catch (error) {
    console.error("Error estimating video tokens:", error);
    return {
      durationSeconds: 60, // Default fallback
      durationFormatted: "1:00",
      tokenEstimate: 18000, // 60 seconds * 300 tokens
      framesPerSecond: 1,
      tokensPerFrame: 258,
      tokensPerSecondAudio: 32
    };
  }
}

/**
 * Count tokens for a video file
 * @param {string} videoPath - Path to the video file
 * @returns {Promise<string>} - Token count information
 */
async function countVideoTokens(videoPath) {
  try {
    const tokenInfo = await estimateVideoTokens(videoPath);

    return `
Video Token Usage Estimate for: ${path.basename(videoPath)}
Duration: ${tokenInfo.durationFormatted}
Sampling rate: ${tokenInfo.framesPerSecond} frame per second
Token usage per frame: ${tokenInfo.tokensPerFrame} tokens
Token usage per second of audio: ${tokenInfo.tokensPerSecondAudio} tokens
Total estimated tokens: ${tokenInfo.tokenEstimate.toLocaleString()} tokens

Note: Gemini processes videos at approximately 300 tokens per second of video.
`;
  } catch (error) {
    console.error("Error counting video tokens:", error);
    return `Error counting video tokens: ${error.message}`;
  }
}

/**
 * Get MIME type from file extension
 * @param {string} extension - File extension
 * @returns {string|null} - MIME type or null if unsupported
 */
function getMimeType(extension) {
  const mimeTypes = {
    '.mp4': 'video/mp4',
    '.mpeg': 'video/mpeg',
    '.mov': 'video/mov',
    '.avi': 'video/avi',
    '.flv': 'video/x-flv',
    '.mpg': 'video/mpg',
    '.webm': 'video/webm',
    '.wmv': 'video/wmv',
    '.3gp': 'video/3gpp'
  };

  return mimeTypes[extension] || null;
}

/**
 * Save transcript to a file in various formats
 * @param {string} text - Transcript text to save
 * @param {string} format - Output format (txt, json, srt, vtt)
 * @param {string} outputPath - Path to save the transcript
 * @param {object} metadata - Additional metadata for the transcript
 * @returns {Promise<string>} - Result message
 */
async function saveTranscript(text, format, outputPath, metadata = {}) {
  try {
    // Create output directory if it doesn't exist
    const outputDir = path.dirname(outputPath);
    await fs.mkdir(outputDir, { recursive: true });

    // Process text based on format
    let formattedContent = '';

    switch (format.toLowerCase()) {
      case 'txt':
        formattedContent = text;
        break;

      case 'json':
        // Extract timestamps if they exist in the text
        const lines = text.split('\n');
        const jsonData = {
          transcript: text,
          lines: [],
          metadata: {
            ...metadata,
            created: new Date().toISOString()
          }
        };

        // Try to parse timestamps in format [HH:MM:SS]
        const timestampRegex = /\[(\d{2}:\d{2}:\d{2})\]|\[(\d{2}:\d{2})\]/;

        lines.forEach(line => {
          const match = line.match(timestampRegex);
          if (match) {
            const timestamp = match[1] || match[2];
            const content = line.replace(timestampRegex, '').trim();
            jsonData.lines.push({
              timestamp,
              content
            });
          } else if (line.trim()) {
            jsonData.lines.push({
              content: line.trim()
            });
          }
        });

        formattedContent = JSON.stringify(jsonData, null, 2);
        break;

      case 'srt':
        // Convert to SubRip format
        const srtLines = text.split('\n');
        let srtContent = [];
        let counter = 1;
        let currentLine = '';
        let previousTimestamp;

        // Try to parse timestamps in format [HH:MM:SS] or [MM:SS]
        const srtTimestampRegex = /\[(\d{2}):(\d{2}):(\d{2})\]|\[(\d{2}):(\d{2})\]/;

        srtLines.forEach(line => {
          const match = line.match(srtTimestampRegex);
          if (match) {
            // If we have content from previous lines, add it to the SRT
            if (currentLine) {
              const startTime = formatSrtTime(previousTimestamp || '00:00:00');
              const endTime = formatSrtTime(match[1] ? `${match[1]}:${match[2]}:${match[3]}` : `00:${match[4]}:${match[5]}`);

              srtContent.push(counter.toString());
              srtContent.push(`${startTime} --> ${endTime}`);
              srtContent.push(currentLine);
              srtContent.push('');

              counter++;
              currentLine = '';
            }

            // Store current timestamp for next entry
            previousTimestamp = match[1] ? `${match[1]}:${match[2]}:${match[3]}` : `00:${match[4]}:${match[5]}`;

            // Add content after timestamp
            currentLine = line.replace(srtTimestampRegex, '').trim();
          } else if (line.trim()) {
            // Append to current line if there's content
            currentLine += currentLine ? ' ' + line.trim() : line.trim();
          }
        });

        // Add the last entry if there's content
        if (currentLine) {
          const startTime = formatSrtTime(previousTimestamp || '00:00:00');
          const endTime = formatSrtTime(addTimeOffset(previousTimestamp || '00:00:00', 5)); // Add 5 seconds for last entry

          srtContent.push(counter.toString());
          srtContent.push(`${startTime} --> ${endTime}`);
          srtContent.push(currentLine);
          srtContent.push('');
        }

        formattedContent = srtContent.join('\n');
        break;

      case 'vtt':
        // Convert to WebVTT format
        formattedContent = 'WEBVTT\n\n';

        // Similar to SRT processing but with WebVTT format
        const vttLines = text.split('\n');
        let vttContent = [];
        let vttCounter = 1;
        let vttCurrentLine = '';
        let vttPreviousTimestamp;

        // Try to parse timestamps in format [HH:MM:SS] or [MM:SS]
        const vttTimestampRegex = /\[(\d{2}):(\d{2}):(\d{2})\]|\[(\d{2}):(\d{2})\]/;

        vttLines.forEach(line => {
          const match = line.match(vttTimestampRegex);
          if (match) {
            // If we have content from previous lines, add it to the VTT
            if (vttCurrentLine) {
              const startTime = formatVttTime(vttPreviousTimestamp || '00:00:00');
              const endTime = formatVttTime(match[1] ? `${match[1]}:${match[2]}:${match[3]}` : `00:${match[4]}:${match[5]}`);

              vttContent.push(`${vttCounter}`);
              vttContent.push(`${startTime} --> ${endTime}`);
              vttContent.push(vttCurrentLine);
              vttContent.push('');

              vttCounter++;
              vttCurrentLine = '';
            }

            // Store current timestamp for next entry
            vttPreviousTimestamp = match[1] ? `${match[1]}:${match[2]}:${match[3]}` : `00:${match[4]}:${match[5]}`;

            // Add content after timestamp
            vttCurrentLine = line.replace(vttTimestampRegex, '').trim();
          } else if (line.trim()) {
            // Append to current line if there's content
            vttCurrentLine += vttCurrentLine ? ' ' + line.trim() : line.trim();
          }
        });

        // Add the last entry if there's content
        if (vttCurrentLine) {
          const startTime = formatVttTime(vttPreviousTimestamp || '00:00:00');
          const endTime = formatVttTime(addTimeOffset(vttPreviousTimestamp || '00:00:00', 5)); // Add 5 seconds for last entry

          vttContent.push(`${vttCounter}`);
          vttContent.push(`${startTime} --> ${endTime}`);
          vttContent.push(vttCurrentLine);
          vttContent.push('');
        }

        formattedContent = 'WEBVTT\n\n' + vttContent.join('\n');
        break;

      default:
        return `Error: Unsupported format "${format}". Supported formats: txt, json, srt, vtt`;
    }

    // Write to file
    await fs.writeFile(outputPath, formattedContent);

    return `Transcript saved successfully to ${outputPath} in ${format} format`;
  } catch (error) {
    console.error("Error saving transcript:", error);
    return `Error saving transcript: ${error.message}`;
  }
}

/**
 * Format time string for SRT format
 * @param {string} timeStr - Time string in format HH:MM:SS
 * @returns {string} - Formatted time string for SRT (HH:MM:SS,000)
 */
function formatSrtTime(timeStr) {
  // Ensure HH:MM:SS format
  const parts = timeStr.split(':');
  if (parts.length === 2) {
    timeStr = `00:${timeStr}`;
  }

  return timeStr.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2:$3,000');
}

/**
 * Format time string for WebVTT format
 * @param {string} timeStr - Time string in format HH:MM:SS
 * @returns {string} - Formatted time string for WebVTT (HH:MM:SS.000)
 */
function formatVttTime(timeStr) {
  // Ensure HH:MM:SS format
  const parts = timeStr.split(':');
  if (parts.length === 2) {
    timeStr = `00:${timeStr}`;
  }

  return timeStr.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2:$3.000');
}

/**
 * Add seconds to a time string
 * @param {string} timeStr - Time string in format HH:MM:SS
 * @param {number} seconds - Seconds to add
 * @returns {string} - New time string
 */
function addTimeOffset(timeStr, seconds) {
  const parts = timeStr.split(':').map(Number);

  // Ensure we have hours, minutes, seconds
  if (parts.length === 2) {
    parts.unshift(0); // Add hours if missing
  }

  // Add seconds
  parts[2] += seconds;

  // Handle overflow
  if (parts[2] >= 60) {
    parts[1] += Math.floor(parts[2] / 60);
    parts[2] %= 60;
  }

  if (parts[1] >= 60) {
    parts[0] += Math.floor(parts[1] / 60);
    parts[1] %= 60;
  }

  // Format back to HH:MM:SS
  return parts.map(p => p.toString().padStart(2, '0')).join(':');
}

function getHelpText() {
  return `
Video Tool Help
==============

The Video Tool allows the agent to analyze and understand videos using Google's Gemini API.

Commands:
  analyze <path>                 - Analyze a video file in detail
  summarize <path>               - Summarize a video concisely
  transcribe <path>              - Transcribe speech in a video with timestamps
  describe <path>                - Provide visual descriptions with timestamps
  timestamp <path> time:MM:SS    - Analyze what happens at a specific timestamp
  quiz <path> [options]          - Create a quiz based on video content
  youtube <url> [prompt:text]    - Analyze a YouTube video
  count <path>                   - Estimate token usage for a video
  saveTranscript                 - Save transcript to file in various formats
  help                           - Show this help text

Options:
  prompt:text       - Custom prompt for analysis
  time:MM:SS        - Timestamp for analysis (e.g., time:01:30)
  questions:number  - Number of questions for quiz (default: 5)
  difficulty:level  - Quiz difficulty (easy, medium, hard)
  format:type       - Format for saving transcript (txt, json, srt, vtt)
  outputPath:path   - Path to save transcript file
  text:content      - Transcript text to save

Examples:
  videoTool analyze "./videos/sample.mp4"
  videoTool summarize "./videos/sample.mp4" prompt:"Summarize this video in 3 sentences"
  videoTool timestamp "./videos/sample.mp4" time:01:45 prompt:"What is happening at this moment?"
  videoTool youtube "https://www.youtube.com/watch?v=9hE5-98ZeCg" prompt:"Analyze this NASA video"
  videoTool quiz "./videos/lecture.mp4" questions:10 difficulty:hard
  videoTool count "./videos/sample.mp4"
  videoTool saveTranscript format:srt outputPath:"./transcripts/video.srt" text:"[00:00:05] Hello world"

Supported video formats: MP4, MPEG, MOV, AVI, FLV, MPG, WEBM, WMV, 3GPP
Supported transcript formats: TXT, JSON, SRT, VTT
`;
}

export { videoTool };
import { GoogleGenAI } from '@google/genai'; // Match GeminiProvider.js import
import kbToolInstance from './tools/kbTool.js';

/**
 * Configuration for the LLMClient.
 * @typedef {object} LLMConfig
 * @property {string} apiKey - The API key for the LLM service.
 * @property {object} [generationConfig] - Default generation configuration.
 * @property {number} [generationConfig.temperature=0.7]
 * @property {number} [generationConfig.topK=40]
 * @property {number} [generationConfig.topP=0.95]
 * @property {number} [generationConfig.maxOutputTokens=4096]
 */

// Constants for Knowledge Base tool function names
const KB_TOOL_FUNCTIONS = {
  KB_GET_ENTRY: 'kb_get_entry',
  KB_LIST_ENTRIES: 'kb_list_entries',
  KB_SEARCH_ENTRIES: 'kb_search_entries',
  KB_ADD_ENTRY: 'kb_add_entry',
};

// Gemini-compatible tool definition for KnowledgeBaseTool
const kbToolDefinition = {
  functionDeclarations: [
    {
      name: KB_TOOL_FUNCTIONS.KB_GET_ENTRY,
      description: "Retrieves a specific entry (e.g., configuration, note, code snippet) from the knowledge base.",
      parameters: {
        type: "OBJECT",
        properties: {
          entry_type: { type: "STRING", description: "The type of the entry. Common types: 'configuration', 'note', 'snippet'." },
          entry_name: { type: "STRING", description: "The unique name or identifier of the entry." }
        },
        required: ["entry_type", "entry_name"]
      }
    },
    {
      name: KB_TOOL_FUNCTIONS.KB_LIST_ENTRIES,
      description: "Lists available entries in the knowledge base, optionally filtered by type.",
      parameters: {
        type: "OBJECT",
        properties: {
          entry_type: { type: "STRING", description: "Optional. The type of entries to list (e.g., 'configuration', 'note', 'snippet'). If omitted, lists all types." }
        }
      }
    },
    {
      name: KB_TOOL_FUNCTIONS.KB_SEARCH_ENTRIES,
      description: "Searches for entries in the knowledge base using keywords, optionally filtered by type.",
      parameters: {
        type: "OBJECT",
        properties: {
          keywords: { type: "STRING", description: "Keywords to search for. Multiple keywords can be space-separated." },
          entry_type: { type: "STRING", description: "Optional. The type of entries to search within (e.g., 'configuration', 'note', 'snippet')." }
        },
        required: ["keywords"]
      }
    },
    {
      name: KB_TOOL_FUNCTIONS.KB_ADD_ENTRY,
      description: "Adds a new entry to the knowledge base.",
      parameters: {
        type: "OBJECT",
        properties: {
          entry_type: { type: "STRING", description: "The type for the new entry (e.g., 'configuration', 'note', 'snippet')." },
          entry_name: { type: "STRING", description: "The unique name or identifier for the new entry." },
          content: { type: "STRING", description: "The main content/text of the new entry." },
          metadata: { type: "OBJECT", description: "Optional. Additional metadata (key-value pairs) for the entry. Should be a flat object or stringified JSON." }
        },
        required: ["entry_type", "entry_name", "content"]
      }
    }
  ]
};

/**
 * A client for interacting with a Large Language Model (LLM),
 * specifically Google's Generative AI.
 */
class LLMClient {
  /**
   * @param {LLMConfig} config - Configuration for the LLM client.
   */
  constructor(config) {
    if (!config || !config.apiKey) {
      throw new Error('LLMClient requires an API key in the configuration.');
    }
    this.ai = new GoogleGenAI({apiKey: config.apiKey}); // Corrected constructor to pass object
    this.defaultGenerationConfig = config.generationConfig || {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 4096,
    };
    this.tools = [kbToolDefinition];
    this.availableTools = {
      [KB_TOOL_FUNCTIONS.KB_GET_ENTRY]: async (args) => kbToolInstance.get({ type: args.entry_type, name: args.entry_name }),
      [KB_TOOL_FUNCTIONS.KB_LIST_ENTRIES]: async (args) => kbToolInstance.list({ type: args.entry_type }),
      [KB_TOOL_FUNCTIONS.KB_SEARCH_ENTRIES]: async (args) => kbToolInstance.search({ keywords: args.keywords, type: args.entry_type }),
      [KB_TOOL_FUNCTIONS.KB_ADD_ENTRY]: async (args) => kbToolInstance.add({ type: args.entry_type, name: args.entry_name, content: args.content, metadata: args.metadata }),
    };
  }

  /**
   * Generates content as a stream from the LLM, handling function calls.
   * @param {object} params - Parameters for content generation.
   * @param {string} params.modelName - The name of the model to use.
   * @param {object[]} params.history - The conversation history.
   * @param {object} [params.generationConfig] - Optional generation config to override defaults.
   * @param {object[]} [params.tools] - Optional tools to provide to the model. Defaults to KB tool.
   * @param {object} [params.toolConfig] - Optional tool configuration.
   * @returns {AsyncIterable<{text?: string, functionCall?: object}>} An async iterable yielding content chunks.
   * @throws {Error} If modelName or history is not provided.
   */
  async *generateContentStream({ modelName, history, generationConfig, tools, toolConfig }) {
    if (!modelName) {
      throw new Error('modelName is required for generateContentStream.');
    }
    if (!history || !Array.isArray(history)) {
      throw new Error('history (array) is required for generateContentStream.');
    }

    const currentGenerationConfig = generationConfig || this.defaultGenerationConfig;
    const currentHistory = [...history]; // Mutable copy for this interaction cycle

    let processingFunctionCallCycle = true;

    while (processingFunctionCallCycle) {
      processingFunctionCallCycle = false; // Assume this is the last iteration unless a function call occurs

      const streamResult = await this.ai.models.generateContentStream({ // Using .models.
        model: modelName,
        contents: currentHistory,
        generationConfig: currentGenerationConfig,
        tools: tools || this.tools,
        toolConfig: toolConfig || { functionCallingConfig: { mode: "AUTO" } } 
      });
      
      let accumulatedFunctionCall = null;

      // Iterate directly over streamResult, assuming it's the async iterable
      for await (const chunk of streamResult) { 
        // Function call handling:
        // The exact structure for function calls with `ai.models.generateContentStream` isn't
        // explicitly shown in GeminiProvider.js. We'll try a common structure.
        // Google's API often puts function calls directly in the chunk or within `parts`.
        // Let's assume `chunk.functionCall` or `chunk.parts[0].functionCall` might exist.
        // This is more robust than `chunk.functionCalls()` if that method isn't on these chunks.
        let fc = null;
        if (chunk.functionCall) {
          fc = chunk.functionCall;
        } else if (chunk.parts && chunk.parts[0] && chunk.parts[0].functionCall) {
          fc = chunk.parts[0].functionCall;
        }
        
        const functionCallsInChunk = fc ? [fc] : null;

        if (functionCallsInChunk && functionCallsInChunk.length > 0) {
          processingFunctionCallCycle = true; 
          
          const currentFc = functionCallsInChunk[0]; 
          if (!accumulatedFunctionCall) {
            accumulatedFunctionCall = { name: currentFc.name, args: { ...(currentFc.args || {}) } };
          } else {
            Object.assign(accumulatedFunctionCall.args, (currentFc.args || {}));
          }

        } else {
          // Text extraction based on GeminiProvider.js
          const textContent = chunk.candidates?.[0]?.content?.parts?.[0]?.text;
          if (textContent !== undefined && textContent !== null && textContent !== '') { // Ensure not empty string
            yield { text: textContent };
          }
        }
      } // End of for-await-of streamResult.stream

      if (processingFunctionCallCycle && accumulatedFunctionCall && accumulatedFunctionCall.name) {
        const functionName = accumulatedFunctionCall.name;
        const functionArgs = accumulatedFunctionCall.args;

        if (this.availableTools[functionName]) {
          console.log(`LLMClient: Calling tool function: ${functionName} with args:`, functionArgs);
          try {
            const toolResult = await this.availableTools[functionName](functionArgs);
            console.log(`LLMClient: Tool ${functionName} result:`, toolResult);

            currentHistory.push({
              role: 'model',
              parts: [{ functionCall: { name: functionName, args: functionArgs } }]
            });
            currentHistory.push({
              role: 'function',
              parts: [{ functionResponse: { name: functionName, response: toolResult } }]
            });
          } catch (error) {
            console.error(`LLMClient: Error executing tool ${functionName}:`, error);
            currentHistory.push({
              role: 'model',
              parts: [{ functionCall: { name: functionName, args: functionArgs } }]
            });
            currentHistory.push({
              role: 'function',
              parts: [{ functionResponse: { name: functionName, response: { error: `Error executing tool: ${error.message}` } } }]
            });
          }
        } else {
          console.warn(`LLMClient: Unknown tool function called: ${functionName}`);
          currentHistory.push({
            role: 'model',
            parts: [{ functionCall: { name: functionName, args: functionArgs } }]
          });
          currentHistory.push({
            role: 'function',
            parts: [{ functionResponse: { name: functionName, response: { error: `Tool ${functionName} not found.` } } }]
          });
          // If an unknown tool is called, we still loop to let the model react to the error.
        }
        accumulatedFunctionCall = null; // Reset for the next iteration of the while loop
      } else if (!processingFunctionCallCycle) {
        // No function call was made in this pass, or it was handled and no new one was made.
        break; 
      }
    } // End of while(processingFunctionCallCycle)
  }

  /**
   * Generates content non-streamed from the LLM, handling function calls.
   * @param {object} params - Parameters for content generation.
   * @param {string} params.modelName - The name of the model to use.
   * @param {object[]} params.history - The conversation history.
   * @param {object} [params.generationConfig] - Optional generation config to override defaults.
   * @param {object[]} [params.tools] - Optional tools.
   * @param {object} [params.toolConfig] - Optional tool configuration.
   * @returns {Promise<string>} The full text response.
   * @throws {Error} If modelName or history is not provided.
   */
  async generateContent({ modelName, history, generationConfig, tools, toolConfig }) {
    if (!modelName) {
      throw new Error('modelName is required for generateContent.');
    }
    if (!history || !Array.isArray(history)) {
      throw new Error('history (array) is required for generateContent.');
    }

    const currentGenerationConfig = generationConfig || this.defaultGenerationConfig;
    let fullReply = '';
    try {
      const stream = this.generateContentStream({
        modelName,
        history,
        generationConfig: currentGenerationConfig,
        tools: tools || this.tools, // Pass along tools
        toolConfig: toolConfig || { functionCallingConfig: { mode: "AUTO" } } // Changed to string literal
      });
      for await (const chunk of stream) {
        if (chunk.text) { // Only accumulate text parts for the final reply
            fullReply += chunk.text;
        }
      }
      return fullReply;
    } catch (error) {
      console.error('Error in LLMClient.generateContent:', error);
      throw error;
    }
  }
}

export default LLMClient;

/**
 * Text-to-Speech Service (Simplified)
 *
 * Provides functionality for converting text to speech using Google Cloud Text-to-Speech.
 * This version uses a single fixed voice setting.
 */

import textToSpeech from '@google-cloud/text-to-speech';
import { Readable } from 'stream';

// Create a client
let client;
try {
  // Create the client using GOOGLE_APPLICATION_CREDENTIALS
  client = new textToSpeech.TextToSpeechClient();
  console.log('Google Cloud Text-to-Speech client initialized successfully');
} catch (error) {
  console.error('Failed to initialize Google Cloud Text-to-Speech client:', error);
  console.error('Error details:', error.message);
}

// Fixed voice settings - using en-IN-Wavenet-F as requested
export const VOICE_SETTINGS = {
  languageCode: 'en-IN',
  voiceName: 'en-IN-Wavenet-F',
  ssmlGender: 'FEMALE',
  audioEncoding: 'MP3',
  speakingRate: 1.0,
  pitch: 0.0
};

/**
 * Split text into sentences for better TTS processing
 * @param {string} text - The text to split
 * @param {number} maxLength - Maximum length of each chunk
 * @returns {string[]} - Array of text chunks
 */
function chunkText(text, maxLength = 4000) {
  // First try to split by sentences
  const sentenceRegex = /[.!?]\s+/g;
  const sentences = text.split(sentenceRegex).filter(s => s.trim().length > 0);

  const chunks = [];
  let currentChunk = "";

  for (const sentence of sentences) {
    // If this sentence alone is too long, we need to split it by words
    if (sentence.length > maxLength) {
      // If we have accumulated text in currentChunk, add it to chunks first
      if (currentChunk.length > 0) {
        chunks.push(currentChunk);
        currentChunk = "";
      }

      // Split the long sentence into word chunks
      let sentenceWithPeriod = sentence + ". "; // Add period for better speech pauses
      let words = sentenceWithPeriod.split(" ");
      let wordChunk = "";

      for (const word of words) {
        if (wordChunk.length + word.length + 1 <= maxLength) {
          wordChunk += (wordChunk.length > 0 ? " " : "") + word;
        } else {
          chunks.push(wordChunk);
          wordChunk = word;
        }
      }

      if (wordChunk.length > 0) {
        chunks.push(wordChunk);
      }
    }
    // If adding this sentence would exceed the max length, start a new chunk
    else if (currentChunk.length + sentence.length + 2 > maxLength) {
      chunks.push(currentChunk);
      currentChunk = sentence + ". ";
    }
    // Otherwise, add the sentence to the current chunk
    else {
      currentChunk += (currentChunk.length > 0 ? " " : "") + sentence + ". ";
    }
  }

  // Add the last chunk if there's anything left
  if (currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  return chunks;
}

/**
 * Synthesize speech for a single chunk of text
 * @param {string} text - The text chunk to convert to speech
 * @param {object} settings - Voice settings
 * @returns {Promise<Buffer>} - The audio buffer
 */
async function synthesizeChunk(text, settings) {
  // Configure the request
  const request = {
    input: { text },
    voice: {
      languageCode: settings.languageCode,
      name: settings.voiceName,
      ssmlGender: settings.ssmlGender
    },
    audioConfig: {
      audioEncoding: settings.audioEncoding,
      speakingRate: settings.speakingRate,
      pitch: settings.pitch
    }
  };

  // Add effectsProfileId if it exists in settings
  if (settings.effectsProfileId) {
    request.audioConfig.effectsProfileId = settings.effectsProfileId;
  }

  // Perform the text-to-speech request
  const [response] = await client.synthesizeSpeech(request);
  return Buffer.from(response.audioContent);
}

/**
 * Synthesize speech from text
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Buffer>} - The audio buffer
 */
export async function synthesizeSpeech(text, voiceSettings = {}) {
  try {
    console.log('TTS disabled - returning null audio buffer');
    return null;
  } catch (error) {
    console.error('Error in synthesizeSpeech:', error);
    throw error;
  }
}

/**
 * Synthesize speech from text and return as a stream
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Readable>} - A readable stream of audio data
 */
export async function synthesizeSpeechStream(text, voiceSettings = {}) {
  try {
    console.log('TTS streaming disabled - returning empty stream');
    const emptyStream = new Readable();
    emptyStream.push(null);
    return emptyStream;
  } catch (error) {
    console.error('Error in synthesizeSpeechStream:', error);
    throw error;
  }
}

export default {
  synthesizeSpeech,
  synthesizeSpeechStream,
  VOICE_SETTINGS
};



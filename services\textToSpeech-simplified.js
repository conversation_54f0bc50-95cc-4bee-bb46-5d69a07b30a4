/**
 * Text-to-Speech Service (Simplified)
 *
 * Provides functionality for converting text to speech using Google Cloud Text-to-Speech.
 * This version uses a single fixed voice setting.
 */

import { Readable } from 'stream';

// TTS client disabled

// TTS settings disabled

// Unused TTS helper functions removed

/**
 * Synthesize speech from text
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Buffer>} - The audio buffer
 */
export async function synthesizeSpeech() {
  console.log('TTS disabled - returning null');
  return null;
}

/**
 * Synthesize speech from text and return as a stream
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Readable>} - A readable stream of audio data
 */
export async function synthesizeSpeechStream() {
  console.log('TTS streaming disabled - returning empty stream');
  const emptyStream = new Readable();
  emptyStream.push(null);
  return emptyStream;
}

export default {
  synthesizeSpeech,
  synthesizeSpeechStream
};



/**
 * Text-to-Speech Service (Simplified)
 *
 * Provides functionality for converting text to speech using Google Cloud Text-to-Speech.
 * This version uses a single fixed voice setting.
 */

import textToSpeech from '@google-cloud/text-to-speech';
import { Readable } from 'stream';
import { synthesizeSpeech as freeSynthesize, synthesizeSpeechStream as freeSynthesizeStream } from './textToSpeech-free.js';

// Create a client
let client;
try {
  // Create the client using GOOGLE_APPLICATION_CREDENTIALS
  client = new textToSpeech.TextToSpeechClient();
  console.log('Google Cloud Text-to-Speech client initialized successfully');
} catch (error) {
  console.error('Failed to initialize Google Cloud Text-to-Speech client:', error);
  console.error('Error details:', error.message);
}

// Fixed voice settings - using en-IN-Wavenet-F as requested
export const VOICE_SETTINGS = {
  languageCode: 'en-IN',
  voiceName: 'en-IN-Wavenet-F',
  ssmlGender: 'FEMALE',
  audioEncoding: 'MP3',
  speakingRate: 1.0,
  pitch: 0.0
};

/**
 * Split text into sentences for better TTS processing
 * @param {string} text - The text to split
 * @param {number} maxLength - Maximum length of each chunk
 * @returns {string[]} - Array of text chunks
 */
function chunkText(text, maxLength = 4000) {
  // First try to split by sentences
  const sentenceRegex = /[.!?]\s+/g;
  const sentences = text.split(sentenceRegex).filter(s => s.trim().length > 0);

  const chunks = [];
  let currentChunk = "";

  for (const sentence of sentences) {
    // If this sentence alone is too long, we need to split it by words
    if (sentence.length > maxLength) {
      // If we have accumulated text in currentChunk, add it to chunks first
      if (currentChunk.length > 0) {
        chunks.push(currentChunk);
        currentChunk = "";
      }

      // Split the long sentence into word chunks
      let sentenceWithPeriod = sentence + ". "; // Add period for better speech pauses
      let words = sentenceWithPeriod.split(" ");
      let wordChunk = "";

      for (const word of words) {
        if (wordChunk.length + word.length + 1 <= maxLength) {
          wordChunk += (wordChunk.length > 0 ? " " : "") + word;
        } else {
          chunks.push(wordChunk);
          wordChunk = word;
        }
      }

      if (wordChunk.length > 0) {
        chunks.push(wordChunk);
      }
    }
    // If adding this sentence would exceed the max length, start a new chunk
    else if (currentChunk.length + sentence.length + 2 > maxLength) {
      chunks.push(currentChunk);
      currentChunk = sentence + ". ";
    }
    // Otherwise, add the sentence to the current chunk
    else {
      currentChunk += (currentChunk.length > 0 ? " " : "") + sentence + ". ";
    }
  }

  // Add the last chunk if there's anything left
  if (currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  return chunks;
}

/**
 * Synthesize speech for a single chunk of text
 * @param {string} text - The text chunk to convert to speech
 * @param {object} settings - Voice settings
 * @returns {Promise<Buffer>} - The audio buffer
 */
async function synthesizeChunk(text, settings) {
  // Configure the request
  const request = {
    input: { text },
    voice: {
      languageCode: settings.languageCode,
      name: settings.voiceName,
      ssmlGender: settings.ssmlGender
    },
    audioConfig: {
      audioEncoding: settings.audioEncoding,
      speakingRate: settings.speakingRate,
      pitch: settings.pitch
    }
  };

  // Add effectsProfileId if it exists in settings
  if (settings.effectsProfileId) {
    request.audioConfig.effectsProfileId = settings.effectsProfileId;
  }

  // Perform the text-to-speech request
  const [response] = await client.synthesizeSpeech(request);
  return Buffer.from(response.audioContent);
}

/**
 * Synthesize speech from text
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Buffer>} - The audio buffer
 */
export async function synthesizeSpeech(text, voiceSettings = {}) {
  try {
    if (!client) {
      console.warn('Google Cloud Text-to-Speech client not initialized, using free TTS fallback');
      return await freeSynthesize(text, voiceSettings);
    }

    if (!text || text.trim().length === 0) {
      console.warn('Empty text provided to synthesizeSpeech');
      return null;
    }

    console.log(`Synthesizing speech for text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

    // Merge voice settings with defaults
    const settings = { ...VOICE_SETTINGS, ...voiceSettings };

    // Split text into chunks if it's too long
    const chunks = chunkText(text);
    console.log(`Text split into ${chunks.length} chunk(s)`);

    if (chunks.length === 1) {
      // Single chunk - synthesize directly
      return await synthesizeChunk(chunks[0], settings);
    } else {
      // Multiple chunks - synthesize each and concatenate
      const audioBuffers = [];
      for (let i = 0; i < chunks.length; i++) {
        console.log(`Synthesizing chunk ${i + 1}/${chunks.length}`);
        const chunkBuffer = await synthesizeChunk(chunks[i], settings);
        audioBuffers.push(chunkBuffer);
      }

      // Concatenate all audio buffers
      const totalLength = audioBuffers.reduce((sum, buffer) => sum + buffer.length, 0);
      const combinedBuffer = Buffer.alloc(totalLength);
      let offset = 0;
      for (const buffer of audioBuffers) {
        buffer.copy(combinedBuffer, offset);
        offset += buffer.length;
      }

      console.log(`Successfully synthesized ${chunks.length} chunks into ${combinedBuffer.length} bytes`);
      return combinedBuffer;
    }
  } catch (error) {
    console.error('Error in Google Cloud TTS, falling back to free TTS:', error);
    try {
      return await freeSynthesize(text, voiceSettings);
    } catch (fallbackError) {
      console.error('Free TTS fallback also failed:', fallbackError);
      throw error; // Throw original error
    }
  }
}

/**
 * Synthesize speech from text and return as a stream
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Readable>} - A readable stream of audio data
 */
export async function synthesizeSpeechStream(text, voiceSettings = {}) {
  try {
    if (!client) {
      console.warn('Google Cloud Text-to-Speech client not initialized, using free TTS stream fallback');
      return await freeSynthesizeStream(text, voiceSettings);
    }

    if (!text || text.trim().length === 0) {
      console.warn('Empty text provided to synthesizeSpeechStream');
      const emptyStream = new Readable();
      emptyStream.push(null);
      return emptyStream;
    }

    console.log(`Synthesizing speech stream for text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

    // For streaming, we'll synthesize the audio first and then create a stream from it
    // This is a simplified approach - a true streaming implementation would use
    // Google's streaming synthesis API if available
    const audioBuffer = await synthesizeSpeech(text, voiceSettings);

    if (!audioBuffer) {
      const emptyStream = new Readable();
      emptyStream.push(null);
      return emptyStream;
    }

    // Handle browser TTS instructions
    if (typeof audioBuffer === 'object' && audioBuffer.type === 'browser-tts') {
      const instructionStream = new Readable();
      instructionStream.push(JSON.stringify(audioBuffer));
      instructionStream.push(null);
      return instructionStream;
    }

    // Create a readable stream from the audio buffer
    const audioStream = new Readable();

    // Push the audio data in chunks for better streaming experience
    const chunkSize = 1024 * 16; // 16KB chunks
    let offset = 0;

    const pushNextChunk = () => {
      if (offset >= audioBuffer.length) {
        audioStream.push(null); // End of stream
        return;
      }

      const chunk = audioBuffer.slice(offset, offset + chunkSize);
      offset += chunkSize;
      audioStream.push(chunk);

      // Use setImmediate to avoid blocking the event loop
      setImmediate(pushNextChunk);
    };

    // Start pushing chunks
    setImmediate(pushNextChunk);

    console.log(`Created audio stream from ${audioBuffer.length} bytes`);
    return audioStream;
  } catch (error) {
    console.error('Error in Google Cloud TTS stream, falling back to free TTS:', error);
    try {
      return await freeSynthesizeStream(text, voiceSettings);
    } catch (fallbackError) {
      console.error('Free TTS stream fallback also failed:', fallbackError);
      throw error; // Throw original error
    }
  }
}

export default {
  synthesizeSpeech,
  synthesizeSpeechStream,
  VOICE_SETTINGS
};



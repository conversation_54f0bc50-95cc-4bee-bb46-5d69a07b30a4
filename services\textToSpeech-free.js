/**
 * Free Text-to-Speech Service
 *
 * Provides TTS functionality using free alternatives:
 * 1. Browser Web Speech API (client-side)
 * 2. eSpeak-NG (server-side, offline)
 * 3. Festival (server-side, offline)
 * 4. Fallback to browser-based synthesis
 */

import { spawn } from 'child_process';
import { Readable } from 'stream';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Voice settings for free TTS
export const FREE_VOICE_SETTINGS = {
  languageCode: 'en-US',
  voiceName: 'default',
  ssmlGender: 'NEUTRAL',
  audioEncoding: 'WAV',
  speakingRate: 1.0,
  pitch: 0.0,
  engine: 'espeak' // 'espeak', 'festival', or 'browser'
};

/**
 * Check if eSpeak-NG is available on the system
 * @returns {Promise<boolean>} - True if eSpeak is available
 */
async function checkEspeakAvailable() {
  return new Promise((resolve) => {
    const espeak = spawn('espeak', ['--version']);
    espeak.on('close', (code) => {
      resolve(code === 0);
    });
    espeak.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * Check if Festival is available on the system
 * @returns {Promise<boolean>} - True if Festival is available
 */
async function checkFestivalAvailable() {
  return new Promise((resolve) => {
    const festival = spawn('festival', ['--version']);
    festival.on('close', (code) => {
      resolve(code === 0);
    });
    festival.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * Synthesize speech using eSpeak-NG
 * @param {string} text - The text to convert to speech
 * @param {object} settings - Voice settings
 * @returns {Promise<Buffer>} - The audio buffer
 */
async function synthesizeWithEspeak(text, settings) {
  return new Promise((resolve, reject) => {
    const outputPath = path.join(__dirname, '..', 'temp', `espeak_${Date.now()}.wav`);
    
    // Ensure temp directory exists
    const tempDir = path.dirname(outputPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // eSpeak command: espeak -w output.wav "text to speak"
    const args = [
      '-w', outputPath,  // Write to WAV file
      '-s', Math.round(settings.speakingRate * 175).toString(), // Speed (words per minute)
      '-p', Math.round(settings.pitch * 50 + 50).toString(), // Pitch (0-99)
      '-v', 'en', // Voice (English)
      text
    ];

    const espeak = spawn('espeak', args);
    
    let stderr = '';
    espeak.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    espeak.on('close', (code) => {
      if (code === 0 && fs.existsSync(outputPath)) {
        try {
          const audioBuffer = fs.readFileSync(outputPath);
          // Clean up temp file
          fs.unlinkSync(outputPath);
          resolve(audioBuffer);
        } catch (error) {
          reject(new Error(`Failed to read eSpeak output: ${error.message}`));
        }
      } else {
        reject(new Error(`eSpeak failed with code ${code}: ${stderr}`));
      }
    });

    espeak.on('error', (error) => {
      reject(new Error(`eSpeak error: ${error.message}`));
    });
  });
}

/**
 * Synthesize speech using Festival
 * @param {string} text - The text to convert to speech
 * @param {object} settings - Voice settings
 * @returns {Promise<Buffer>} - The audio buffer
 */
async function synthesizeWithFestival(text, settings) {
  return new Promise((resolve, reject) => {
    const outputPath = path.join(__dirname, '..', 'temp', `festival_${Date.now()}.wav`);
    
    // Ensure temp directory exists
    const tempDir = path.dirname(outputPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Festival command using text2wave
    const festival = spawn('text2wave', ['-o', outputPath]);
    
    let stderr = '';
    festival.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Send text to Festival via stdin
    festival.stdin.write(text);
    festival.stdin.end();

    festival.on('close', (code) => {
      if (code === 0 && fs.existsSync(outputPath)) {
        try {
          const audioBuffer = fs.readFileSync(outputPath);
          // Clean up temp file
          fs.unlinkSync(outputPath);
          resolve(audioBuffer);
        } catch (error) {
          reject(new Error(`Failed to read Festival output: ${error.message}`));
        }
      } else {
        reject(new Error(`Festival failed with code ${code}: ${stderr}`));
      }
    });

    festival.on('error', (error) => {
      reject(new Error(`Festival error: ${error.message}`));
    });
  });
}

/**
 * Generate browser-compatible TTS instructions
 * This returns instructions for the client to use Web Speech API
 * @param {string} text - The text to convert to speech
 * @param {object} settings - Voice settings
 * @returns {object} - Browser TTS instructions
 */
function generateBrowserTTSInstructions(text, settings) {
  return {
    type: 'browser-tts',
    text: text,
    voice: {
      lang: settings.languageCode || 'en-US',
      rate: settings.speakingRate || 1.0,
      pitch: settings.pitch || 1.0,
      volume: 1.0
    },
    instructions: 'Use Web Speech API speechSynthesis.speak() on the client side'
  };
}

/**
 * Split text into sentences for better TTS processing
 * @param {string} text - The text to split
 * @param {number} maxLength - Maximum length of each chunk
 * @returns {string[]} - Array of text chunks
 */
function chunkText(text, maxLength = 200) {
  // For free TTS, use smaller chunks to avoid timeouts
  const sentenceRegex = /[.!?]\s+/g;
  const sentences = text.split(sentenceRegex).filter(s => s.trim().length > 0);

  const chunks = [];
  let currentChunk = "";

  for (const sentence of sentences) {
    if (sentence.length > maxLength) {
      // If a single sentence is too long, split by words
      if (currentChunk.length > 0) {
        chunks.push(currentChunk);
        currentChunk = "";
      }
      
      const words = sentence.split(' ');
      let wordChunk = "";
      
      for (const word of words) {
        if (wordChunk.length + word.length + 1 <= maxLength) {
          wordChunk += (wordChunk.length > 0 ? " " : "") + word;
        } else {
          chunks.push(wordChunk);
          wordChunk = word;
        }
      }

      if (wordChunk.length > 0) {
        chunks.push(wordChunk);
      }
    }
    else if (currentChunk.length + sentence.length + 2 > maxLength) {
      chunks.push(currentChunk);
      currentChunk = sentence + ". ";
    }
    else {
      currentChunk += (currentChunk.length > 0 ? " " : "") + sentence + ". ";
    }
  }

  if (currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  return chunks;
}

/**
 * Synthesize speech using free TTS engines
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Buffer|object>} - The audio buffer or browser instructions
 */
export async function synthesizeSpeech(text, voiceSettings = {}) {
  try {
    if (!text || text.trim().length === 0) {
      console.warn('Empty text provided to free TTS synthesizeSpeech');
      return null;
    }

    console.log(`Free TTS: Synthesizing speech for text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

    // Merge voice settings with defaults
    const settings = { ...FREE_VOICE_SETTINGS, ...voiceSettings };

    // Check available engines and use the best one
    const espeakAvailable = await checkEspeakAvailable();
    const festivalAvailable = await checkFestivalAvailable();

    console.log(`Free TTS: eSpeak available: ${espeakAvailable}, Festival available: ${festivalAvailable}`);

    // Split text into chunks
    const chunks = chunkText(text);
    console.log(`Free TTS: Text split into ${chunks.length} chunk(s)`);

    // Try engines in order of preference
    if (espeakAvailable && settings.engine !== 'festival') {
      console.log('Free TTS: Using eSpeak-NG');
      
      if (chunks.length === 1) {
        return await synthesizeWithEspeak(chunks[0], settings);
      } else {
        // For multiple chunks, synthesize each and concatenate
        const audioBuffers = [];
        for (let i = 0; i < chunks.length; i++) {
          console.log(`Free TTS: Synthesizing chunk ${i + 1}/${chunks.length} with eSpeak`);
          const chunkBuffer = await synthesizeWithEspeak(chunks[i], settings);
          audioBuffers.push(chunkBuffer);
        }

        // Simple concatenation (note: this may not work perfectly for WAV files)
        const totalLength = audioBuffers.reduce((sum, buffer) => sum + buffer.length, 0);
        const combinedBuffer = Buffer.alloc(totalLength);
        let offset = 0;
        for (const buffer of audioBuffers) {
          buffer.copy(combinedBuffer, offset);
          offset += buffer.length;
        }

        console.log(`Free TTS: Successfully synthesized ${chunks.length} chunks with eSpeak`);
        return combinedBuffer;
      }
    } else if (festivalAvailable && settings.engine !== 'espeak') {
      console.log('Free TTS: Using Festival');
      return await synthesizeWithFestival(text, settings);
    } else {
      console.log('Free TTS: No offline engines available, returning browser instructions');
      // Return instructions for browser-based TTS
      return generateBrowserTTSInstructions(text, settings);
    }
  } catch (error) {
    console.error('Error in free TTS synthesizeSpeech:', error);
    console.log('Free TTS: Falling back to browser instructions due to error');
    // Fallback to browser instructions
    return generateBrowserTTSInstructions(text, voiceSettings);
  }
}

/**
 * Synthesize speech and return as a stream (free version)
 * @param {string} text - The text to convert to speech
 * @param {object} voiceSettings - Optional voice settings to override defaults
 * @returns {Promise<Readable>} - A readable stream of audio data or instructions
 */
export async function synthesizeSpeechStream(text, voiceSettings = {}) {
  try {
    console.log(`Free TTS: Creating speech stream for text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

    const result = await synthesizeSpeech(text, voiceSettings);
    
    if (!result) {
      const emptyStream = new Readable();
      emptyStream.push(null);
      return emptyStream;
    }

    // If result is browser instructions, return them as a JSON stream
    if (result.type === 'browser-tts') {
      const instructionStream = new Readable();
      instructionStream.push(JSON.stringify(result));
      instructionStream.push(null);
      return instructionStream;
    }

    // If result is an audio buffer, create a stream from it
    if (Buffer.isBuffer(result)) {
      const audioStream = new Readable();
      
      // Push the audio data in chunks
      const chunkSize = 1024 * 8; // 8KB chunks
      let offset = 0;
      
      const pushNextChunk = () => {
        if (offset >= result.length) {
          audioStream.push(null); // End of stream
          return;
        }
        
        const chunk = result.slice(offset, offset + chunkSize);
        offset += chunkSize;
        audioStream.push(chunk);
        
        setImmediate(pushNextChunk);
      };
      
      setImmediate(pushNextChunk);
      
      console.log(`Free TTS: Created audio stream from ${result.length} bytes`);
      return audioStream;
    }

    // Fallback
    const emptyStream = new Readable();
    emptyStream.push(null);
    return emptyStream;
  } catch (error) {
    console.error('Error in free TTS synthesizeSpeechStream:', error);
    const emptyStream = new Readable();
    emptyStream.push(null);
    return emptyStream;
  }
}

export default {
  synthesizeSpeech,
  synthesizeSpeechStream,
  FREE_VOICE_SETTINGS
};

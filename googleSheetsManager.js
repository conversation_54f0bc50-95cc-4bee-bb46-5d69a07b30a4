import dotenv from 'dotenv';
dotenv.config();
import fs from 'fs/promises';
import path from 'path';
import process from 'process';
import { authenticate } from '@google-cloud/local-auth';
import { google } from 'googleapis';

// If modifying these scopes, delete token.json.
const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];
// The file token.json stores the user's access and refresh tokens, and is
// created automatically when the authorization flow completes for the first time.
const TOKEN_PATH = path.join(process.cwd(), 'token.json');
const CREDENTIALS_PATH = path.join(process.cwd(), 'sheeis2.json'); // Updated to use sheeis2.json

/**
 * Reads previously authorized credentials from the save file.
 *
 * @return {Promise<OAuth2Client|null>}
 */
async function loadSavedCredentialsIfExist() {
  try {
    const content = await fs.readFile(TOKEN_PATH);
    const credentials = JSON.parse(content);
    return google.auth.fromJSON(credentials);
  } catch (err) {
    // console.log('Error loading saved credentials:', err.message);
    return null;
  }
}

/**
 * Serializes credentials to a file comptible with GoogleAUth.fromJSON.
 *
 * @param {OAuth2Client} client
 * @return {Promise<void>}
 */
async function saveCredentials(client) {
  try {
    const content = await fs.readFile(CREDENTIALS_PATH);
    const keys = JSON.parse(content);
    const key = keys.installed || keys.web; // 'installed' for desktop, 'web' for web app
    const payload = JSON.stringify({
      type: 'authorized_user',
      client_id: key.client_id,
      client_secret: key.client_secret,
      refresh_token: client.credentials.refresh_token,
    });
    await fs.writeFile(TOKEN_PATH, payload);
    console.log('Token saved to', TOKEN_PATH);
  } catch (err) {
    console.error('Error saving credentials:', err.message);
    throw new Error('Failed to save credentials. Ensure credentials2.json is valid.');
  }
}

/**
 * Load or request or authorization to call APIs.
 *
 */
async function authorize() {
  let client = await loadSavedCredentialsIfExist();
  if (client) {
    // Check if the access token is expired. If so, refresh it.
    // The google.auth.fromJSON method should ideally handle this,
    // but we can add an explicit check if needed or rely on the API calls to fail and then refresh.
    // For simplicity, we'll assume the library handles refresh token usage if the access token is expired.
    // If not, an explicit refresh call would be:
    // if (client.isTokenExpiring() || !client.credentials.access_token) {
    //   try {
    //     await client.refreshAccessToken();
    //     await saveCredentials(client); // Save the new token
    //   } catch (refreshError) {
    //     console.error('Failed to refresh access token, re-authenticating:', refreshError);
    //     client = null; // Force re-authentication
    //   }
    // }
    if (client) return client;
  }

  // If no valid saved client, authenticate
  try {
    console.log(`Attempting to authenticate using credentials from: ${CREDENTIALS_PATH} on port 3081`);
    client = await authenticate({
      scopes: SCOPES,
      keyfilePath: CREDENTIALS_PATH,
      port: 3081, // Explicitly set the port
    });
    if (client.credentials) {
      await saveCredentials(client);
    }
    return client;
  } catch (err) {
    console.error('Error during authentication:', err.message);
    if (err.message.includes('invalid_grant')) {
        console.error('This could be due to an invalid refresh token (token.json might be stale or corrupted), or an issue with your OAuth client configuration in Google Cloud Console.');
        console.error('Try deleting token.json and running the script again to re-authorize.');
    } else if (err.message.includes('ENOENT') && err.message.includes(CREDENTIALS_PATH)) {
        console.error(`Error: Credentials file '${CREDENTIALS_PATH}' not found.`);
        console.error(`Please download your OAuth 2.0 client secrets file from Google Cloud Console`);
        console.error(`and save it as '${path.basename(CREDENTIALS_PATH)}' in the current directory.`);
    } else if (err.message.includes('The provided keyfile does not define a valid redirect URI')) {
        // Already logged by the library, but we can add context
        console.error(`This usually means the OAuth Client ID in GCP for '${path.basename(CREDENTIALS_PATH)}' (likely a 'Web application' type) needs an 'Authorized redirect URI' like 'http://localhost:3081' (or the port you specified). Ensure this is set in GCP and the JSON file is re-downloaded.`);
    }
    return null;
  }
}

/**
 * Lists the names of sheets in a sample spreadsheet.
 * @param {google.auth.OAuth2} auth An authorized OAuth2 client.
 * @param {string} spreadsheetId The ID of the spreadsheet.
 */
async function listSheetTitles(auth, spreadsheetId) {
  if (!auth) {
    console.log('Authentication failed. Cannot list sheets.');
    return;
  }
  const sheets = google.sheets({version: 'v4', auth});
  try {
    const res = await sheets.spreadsheets.get({
      spreadsheetId: spreadsheetId,
    });
    const sheetObjects = res.data.sheets;
    if (!sheetObjects || sheetObjects.length === 0) {
      console.log('No sheets found in this spreadsheet.');
      return;
    }
    console.log(`Sheets in spreadsheet '${spreadsheetId}':`);
    sheetObjects.forEach((sheet) => {
      console.log(`- ${sheet.properties.title}`);
    });
  } catch (err) {
    console.error('The API returned an error: ' + err);
    if (err.response && err.response.data && err.response.data.error) {
        const errorDetails = err.response.data.error;
        console.error(`Error details: ${errorDetails.message} (Code: ${errorDetails.code}, Status: ${errorDetails.status})`);
        if (errorDetails.status === 'PERMISSION_DENIED') {
            console.error("Ensure the account associated with your credentials (via token.json/credentials2.json)");
            console.error(`has at least read access to the spreadsheet: ${spreadsheetId}`);
            console.error("You might need to share the Google Sheet with the email address of your OAuth client or service account.");
        }
    }
  }
}

async function main() {
    console.log("Authorizing with Google Sheets API...");
    const authClient = await authorize();

    if (authClient) {
        console.log("Successfully authorized.");
        
        const SPREADSHEET_ID = process.env.SPREADSHEET_ID;

        if (!SPREADSHEET_ID) {
            console.error("Error: SPREADSHEET_ID not found in your .env file or environment variables.");
            console.error("Please ensure your .env file contains SPREADSHEET_ID=your_actual_id");
            console.error("Or that the environment variable SPREADSHEET_ID is set.");
        } else if (SPREADSHEET_ID === 'YOUR_SPREADSHEET_ID_HERE_FROM_ENV_EXAMPLE') { 
            // This condition might be for a default example value in .env, adjust if needed
            console.log("\nPlease ensure SPREADSHEET_ID in your .env file is set to an actual Google Spreadsheet ID.");
        } else {
            console.log(`\nAttempting to list sheets for spreadsheet ID from .env: ${SPREADSHEET_ID}`);
            await listSheetTitles(authClient, SPREADSHEET_ID);
        }
    } else {
        console.log("Failed to authorize with Google Sheets API.");
        console.log(`Please check your '${path.basename(CREDENTIALS_PATH)}' file and ensure you have an internet connection.`);
        console.log(`If '${path.basename(CREDENTIALS_PATH)}' is for a 'Web application', ensure your redirect URIs (e.g., http://localhost:3081 or another port you've configured) are correctly set up in GCP and match what the authentication library expects.`);
        console.log("The '@google-cloud/local-auth' library typically opens a browser and starts a local server to handle the OAuth redirect.");
    }
}

// To check if the script is run directly in ES modules:
// import { fileURLToPath } from 'url';
// const __filename = fileURLToPath(import.meta.url);
// if (process.argv[1] === __filename) { // This check might need adjustment based on execution context
//   main().catch(console.error);
// }
// A simpler check for direct execution in many cases, or just call main() if it's always meant to be an executable.
// For this script's purpose, we'll assume it's primarily run directly.
main().catch(console.error);


// ES module exports (if you intend to import these functions elsewhere)
export { authorize, listSheetTitles };

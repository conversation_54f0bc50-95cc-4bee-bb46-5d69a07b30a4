import dotenv from 'dotenv';
import { synthesizeSpeech, synthesizeSpeechStream } from './services/textToSpeech-simplified.js';
import fs from 'fs';

// Load environment variables
dotenv.config();

async function testFreeTTS() {
  console.log("Testing Free TTS functionality...");
  
  try {
    // Test 1: Basic TTS synthesis (should fallback to free TTS)
    console.log("\n--- Test 1: Free TTS synthesis ---");
    const testText = "Hello, this is a test of the free text-to-speech functionality.";
    console.log(`Testing with text: "${testText}"`);
    
    const result = await synthesizeSpeech(testText);
    
    if (result) {
      if (Buffer.isBuffer(result)) {
        console.log(`✅ Free TTS synthesis successful! Generated ${result.length} bytes of audio`);
        
        // Save the audio to a file for verification
        const outputPath = './test-free-output.wav';
        fs.writeFileSync(outputPath, result);
        console.log(`Audio saved to: ${outputPath}`);
      } else if (typeof result === 'object' && result.type === 'browser-tts') {
        console.log(`✅ Free TTS returned browser instructions:`, result);
        console.log(`Instructions: ${result.instructions}`);
        console.log(`Voice settings:`, result.voice);
      } else {
        console.log(`✅ Free TTS returned result:`, typeof result, result);
      }
    } else {
      console.log("❌ Free TTS synthesis returned null");
    }
    
    // Test 2: Free TTS streaming
    console.log("\n--- Test 2: Free TTS streaming ---");
    const streamText = "This is a test of the free streaming text-to-speech functionality.";
    console.log(`Testing stream with text: "${streamText}"`);
    
    const audioStream = await synthesizeSpeechStream(streamText);
    
    if (audioStream) {
      console.log("✅ Free TTS streaming successful! Stream created");
      
      // Test reading from the stream
      let streamData = Buffer.alloc(0);
      let isJson = false;
      
      audioStream.on('data', (chunk) => {
        // Check if this looks like JSON data
        if (chunk.toString().startsWith('{')) {
          isJson = true;
          try {
            const instructions = JSON.parse(chunk.toString());
            console.log('📱 Received browser TTS instructions:', instructions);
          } catch (e) {
            console.log('📱 Received data that looks like JSON but failed to parse');
          }
        } else {
          streamData = Buffer.concat([streamData, chunk]);
        }
      });
      
      audioStream.on('end', () => {
        if (isJson) {
          console.log('Stream completed with browser TTS instructions');
        } else {
          console.log(`Stream completed with ${streamData.length} bytes`);
          
          if (streamData.length > 0) {
            // Save the streamed audio to a file for verification
            const streamOutputPath = './test-free-stream-output.wav';
            fs.writeFileSync(streamOutputPath, streamData);
            console.log(`Streamed audio saved to: ${streamOutputPath}`);
          }
        }
      });
      
      audioStream.on('error', (error) => {
        console.error("❌ Stream error:", error);
      });
    } else {
      console.log("❌ Free TTS streaming returned null");
    }
    
    // Test 3: Voice settings
    console.log("\n--- Test 3: Free TTS with custom voice settings ---");
    const customText = "Testing custom voice settings with free TTS.";
    const customSettings = {
      languageCode: 'en-US',
      speakingRate: 0.8,
      pitch: 0.2,
      engine: 'browser'
    };
    
    console.log(`Testing with custom settings:`, customSettings);
    
    const customResult = await synthesizeSpeech(customText, customSettings);
    
    if (customResult) {
      if (Buffer.isBuffer(customResult)) {
        console.log(`✅ Custom settings TTS successful! Generated ${customResult.length} bytes`);
      } else if (typeof customResult === 'object' && customResult.type === 'browser-tts') {
        console.log(`✅ Custom settings returned browser instructions:`, customResult);
      }
    } else {
      console.log("❌ Custom settings TTS returned null");
    }
    
    console.log("\n🎉 Free TTS testing completed!");
    console.log("\n📋 Summary:");
    console.log("- Free TTS provides fallback when Google Cloud TTS is not available");
    console.log("- Browser TTS instructions are returned when offline engines are not available");
    console.log("- The browser will use Web Speech API for actual audio synthesis");
    console.log("- This provides completely free TTS functionality!");
    
  } catch (error) {
    console.error("❌ Free TTS test failed:", error);
    console.error("Error details:", error.message);
  }
}

// Run the test
testFreeTTS();

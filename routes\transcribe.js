/**
 * Transcribe Routes
 *
 * Handles all routes related to speech-to-text functionality.
 */

import express from 'express';
import multer from 'multer';
import mongoose from 'mongoose';
import { transcribeSpeech, checkGoogleCredentials } from '../services/speechToText.js';
import Transcript from '../models/transcript.js';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  }
});

/**
 * POST /api/transcribe
 * Transcribes an audio file to text
 */
router.post('/', upload.single('audio'), async (req, res) => {
  console.log('STT endpoint disabled');
  res.status(503).json({
    error: 'Speech-to-text service is disabled',
    disabled: true
  });
});

/**
 * GET /api/transcribe/diagnostics
 * Returns diagnostic information about the speech-to-text service
 */
router.get('/diagnostics', (_req, res) => {
  res.json({
    status: 'disabled',
    message: 'Speech-to-text service is disabled',
    timestamp: new Date().toISOString()
  });
});

export default router;

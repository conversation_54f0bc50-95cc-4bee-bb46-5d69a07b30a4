import speech from '@google-cloud/speech';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name (for ES modules)
const __filename = fileURLToPath(import.meta.url);

// Create a client using the credentials set in app.js
let client;
try {
  // Create the client using GOOGLE_APPLICATION_CREDENTIALS
  client = new speech.SpeechClient();
  console.log('Google Speech-to-Text client initialized successfully');
} catch (error) {
  console.error('Failed to initialize Google Speech-to-Text client:', error);
  console.error('Error details:', error.message);
}

/**
 * Transcribe speech from an audio buffer
 * @param {Buffer} audioBuffer - The audio buffer to transcribe
 * @param {string} contentType - The MIME type of the audio (e.g., 'audio/wav', 'audio/mp3')
 * @returns {Promise<object>} - The transcription result
 */
export async function transcribeSpeech() {
  console.log('STT disabled - returning fallback response');
  return {
    text: "Speech-to-text is disabled",
    confidence: 0.0,
    audioLength: 0,
    disabled: true
  };
}

/**
 * Fallback transcription when Google Cloud Speech-to-Text is not available
 * This is a simple mock implementation that returns a predefined response
 * @param {Buffer} audioBuffer - The audio buffer
 * @param {string} contentType - The content type
 * @returns {Promise<object>} - A mock transcription result
 */
async function useFallbackTranscription(audioBuffer, contentType) {
  console.log('Using fallback transcription mode');
  console.log(`Audio buffer size: ${audioBuffer ? audioBuffer.length : 'null'} bytes`);
  console.log(`Content type: ${contentType || 'not provided'}`);

  // Check if we have GOOGLE_CLOUD_CREDENTIALS but it's not being parsed correctly
  let credentialIssue = '';
  if (process.env.GOOGLE_CLOUD_CREDENTIALS) {
    try {
      // Try to parse the credentials to see if that's the issue
      JSON.parse(process.env.GOOGLE_CLOUD_CREDENTIALS.replace(/^'|'$/g, ''));
      credentialIssue = 'Credentials are present but not being used correctly.';
    } catch (e) {
      credentialIssue = `Credentials are present but not valid JSON: ${e.message}`;
    }
  } else {
    credentialIssue = 'GOOGLE_CLOUD_CREDENTIALS environment variable is not set.';
  }

  // In a real implementation, you might want to use a different speech-to-text service
  // or implement a simple speech recognition algorithm

  // For now, we'll just return a mock response with diagnostic information
  return {
    text: `I received your audio but I'm using fallback mode because Google Cloud Speech-to-Text is not configured properly. ${credentialIssue}`,
    confidence: 1.0,
    audioLength: audioBuffer ? audioBuffer.length : 0,
    fallbackMode: true,
    diagnosticInfo: {
      hasGoogleCloudCredentials: !!process.env.GOOGLE_CLOUD_CREDENTIALS,
      hasGoogleApplicationCredentials: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
      contentType: contentType || 'not provided'
    }
  };
}

/**
 * Utility function to check and debug Google Cloud credentials
 * @returns {object} - Information about the credentials
 */
export function checkGoogleCredentials() {
  const result = {
    hasGoogleCloudCredentials: !!process.env.GOOGLE_CLOUD_CREDENTIALS,
    hasGoogleApplicationCredentials: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
    clientInitialized: !!client,
    googleCloudCredentialsValid: false,
    googleApplicationCredentialsValid: false,
    issues: []
  };

  if (process.env.GOOGLE_CLOUD_CREDENTIALS) {
    try {
      // Check if the credentials can be parsed as JSON
      const credStr = process.env.GOOGLE_CLOUD_CREDENTIALS.replace(/^'|'$/g, '');
      const creds = JSON.parse(credStr);

      // Check if the credentials have the required fields
      const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
      const missingFields = requiredFields.filter(field => !creds[field]);

      if (missingFields.length > 0) {
        result.issues.push(`GOOGLE_CLOUD_CREDENTIALS is missing required fields: ${missingFields.join(', ')}`);
      } else {
        result.googleCloudCredentialsValid = true;
      }
    } catch (e) {
      result.issues.push(`GOOGLE_CLOUD_CREDENTIALS is not valid JSON: ${e.message}`);
      // Log a small portion of the credentials to help debug
      const credStr = process.env.GOOGLE_CLOUD_CREDENTIALS;
      result.credentialPreview = credStr.substring(0, 50) + '...';
    }
  } else {
    result.issues.push('GOOGLE_CLOUD_CREDENTIALS is not set');
  }

  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    // Check if the file exists
    try {
      const filePath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
      if (fs.existsSync(filePath)) {
        result.googleApplicationCredentialsValid = true;
      } else {
        result.issues.push(`GOOGLE_APPLICATION_CREDENTIALS file does not exist: ${filePath}`);
      }
    } catch (e) {
      result.issues.push(`Error checking GOOGLE_APPLICATION_CREDENTIALS: ${e.message}`);
    }
  } else {
    result.issues.push('GOOGLE_APPLICATION_CREDENTIALS is not set');
  }

  return result;
}

export default {
  transcribeSpeech,
  checkGoogleCredentials
};

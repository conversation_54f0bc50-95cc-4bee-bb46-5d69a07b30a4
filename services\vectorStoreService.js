import { Pinecone } from '@pinecone-database/pinecone';

const PINECONE_API_KEY = process.env.PINECONE_API_KEY;
const PINECONE_HOST_URL = process.env.PINECONE_HOST_URL;
const PINECONE_INDEX_NAME = process.env.PINECONE_INDEX_NAME || 'varjis'; // Read from .env, fallback to 'varjis'
const PINECONE_NAMESPACE = 'chat-history'; // Default namespace for chat history

let pineconeClient;
let pineconeIndex;

try {
  if (PINECONE_API_KEY && PINECONE_HOST_URL && PINECONE_INDEX_NAME) {
    pineconeClient = new Pinecone({
      apiKey: PINECONE_API_KEY,
      // The Pinecone client v3+ typically infers the environment from the host URL
      // or you might need to provide `environment` if it can't.
      // For a full host URL, direct usage is often supported.
    });
    // The host parameter in Index() is used if you need to override the default
    // host constructed from index name and project details, or if connecting to a gRPC endpoint directly.
    // Given we have the full host URL, we can target the index and it should use this host.
    pineconeIndex = pineconeClient.Index(PINECONE_INDEX_NAME);
    // It's good practice to ensure the index is ready, perhaps with a describeIndexStats call
    // but for now, we'll assume it connects.
    console.log(`Pinecone vector store service initialized for index: ${PINECONE_INDEX_NAME} at host: ${PINECONE_HOST_URL}`);
  } else {
    let missingVars = [];
    if (!PINECONE_API_KEY) missingVars.push("PINECONE_API_KEY");
    if (!PINECONE_HOST_URL) missingVars.push("PINECONE_HOST_URL");
    if (!PINECONE_INDEX_NAME && !process.env.PINECONE_INDEX_NAME) missingVars.push("PINECONE_INDEX_NAME (or it defaulted to 'varjis' but other vars are missing)");
    
    console.warn(`CRITICAL: Pinecone configuration incomplete. Missing: ${missingVars.join(', ')}. Vector store service will not work.`);
  }
} catch (error) {
  console.error("Error initializing Pinecone client:", error);
  // pineconeClient and pineconeIndex will remain undefined
}

export async function upsertToPinecone(sessionId, messageId, vector, metadata) {
  if (!pineconeIndex) {
    console.error("Pinecone index is not initialized. Cannot upsert vector.");
    throw new Error("Pinecone index is not initialized.");
  }
  if (!sessionId || !messageId || !vector || !metadata) {
    console.error("Missing required parameters for upsertToPinecone.");
    throw new Error("Missing required parameters for upsertToPinecone (sessionId, messageId, vector, metadata).");
  }

  try {
    const record = {
      id: messageId, // Pinecone requires string IDs
      values: vector,
      metadata: { ...metadata, sessionId } // Ensure sessionId is part of the metadata for filtering
    };
    await pineconeIndex.namespace(PINECONE_NAMESPACE).upsert([record]);
    console.log(`Upserted message ${messageId} (session: ${sessionId}) to Pinecone namespace: ${PINECONE_NAMESPACE}.`);
  } catch (error) {
    console.error(`Error upserting vector ${messageId} to Pinecone for session ${sessionId}:`, error);
    // Consider the nature of the error, e.g., if it's a connection issue or data format issue.
    throw error; // Re-throw to allow caller to handle
  }
}

export async function queryFromPinecone(sessionId, queryVector, topK) {
  if (!pineconeIndex) {
    console.error("Pinecone index is not initialized. Cannot query vector.");
    throw new Error("Pinecone index is not initialized.");
  }
  if (!sessionId || !queryVector || !topK) {
    console.error("Missing required parameters for queryFromPinecone.");
    throw new Error("Missing required parameters for queryFromPinecone (sessionId, queryVector, topK).");
  }

  try {
    const queryResponse = await pineconeIndex.namespace(PINECONE_NAMESPACE).query({
      vector: queryVector,
      topK: topK,
      includeValues: false, // Usually not needed for RAG context, saves bandwidth
      includeMetadata: true, // Essential to get the original text and other info
      filter: { sessionId: { '$eq': sessionId } }
    });
    console.log(`Pinecone query for session ${sessionId} (topK: ${topK}) returned ${queryResponse.matches?.length || 0} matches.`);
    return queryResponse.matches || [];
  } catch (error) {
    console.error(`Error querying Pinecone for session ${sessionId}:`, error);
    // Consider the nature of the error
    throw error; // Re-throw to allow caller to handle
  }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Varjis UI</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Markdown parser -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- CSS -->
     <link rel="stylesheet" href="/css/style.css">
    <!-- Syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/styles/default.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/highlight.min.js"></script>

    <!-- and it's easy to individually load additional languages -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/go.min.js"></script>

    <script>hljs.highlightAll();</script>
    <!-- Time display styles -->
    <link rel="stylesheet" href="/css/time-display.css">

</head>
<body class="flex h-screen overflow-hidden">
    <!-- Sidebar overlay for mobile -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>
    <!-- Left Sidebar - Toggleable between t1 and t2 styles -->
    <div id="sidebar" class="sidebar collapsed bg-gray-900 flex flex-col py-4 px-2">
        <!-- Toggle Button -->
        <div class="flex items-center justify-between mb-4 px-2">
            <button id="toggleSidebar" class="text-gray-400 focus:outline-none">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Collapsed Sidebar Content (t1 style) -->
        <div class="sidebar-collapsed-content flex flex-col items-center mt-8 space-y-6">
            <div id="newChatButtonCollapsed" class="sidebar-icon p-3 rounded-full cursor-pointer">
                <i class="fas fa-plus text-gray-400"></i>
            </div>
            <div class="sidebar-icon p-3 rounded-full cursor-pointer">
                <i class="fas fa-history text-gray-400"></i>
            </div>
            <div class="sidebar-icon p-3 rounded-full cursor-pointer">
                <i class="fas fa-cog text-gray-400"></i>
            </div>
        </div>

        <!-- Expanded Sidebar Content (t2 style) -->
        <div class="sidebar-expanded-content">
            <button id="newChatButton" class="flex items-center gap-2 text-white text-sm px-2 py-2 hover:bg-gray-800 rounded w-full">
                <i class="fas fa-plus text-gray-400"></i>
                <span class="sidebar-item-text">New chat</span>
            </button>

            <div class="mt-6 text-xs text-gray-400 px-2">Recent Conversations</div>
            <ul id="conversationsList" class="mt-2 space-y-1 px-2 text-sm">
                <!-- Conversations will be loaded here dynamically -->
                <li class="flex items-center justify-center py-4 text-gray-400">
                    <span class="text-xs">Loading conversations...</span>
                </li>
            </ul>

            <div class="mt-6 text-xs text-gray-400 px-2">Personas</div>
            <ul class="mt-2 space-y-1 px-2 text-sm">
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <i class="fas fa-user-circle text-gray-400"></i>
                    <span class="sidebar-item-text">Current: <span id="currentPersona">Default</span></span>
                </li>
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <i class="fas fa-exchange-alt text-gray-400"></i>
                    <select id="personaSelector" class="bg-gray-800 text-white text-sm rounded px-2 py-1 w-full">
                        <option value="default">Default Varjis</option>
                        <option value="professional">Professional Varjis</option>
                        <option value="casual">Casual Varjis</option>
                        <option value="varjis_llm">J.A.R.V.I.S.</option>
                    </select>
                </li>
            </ul>

            <div class="mt-6 text-xs text-gray-400 px-2">Gems</div>
            <ul class="mt-2 space-y-1 px-2 text-sm">
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <i class="fas fa-gem text-gray-400"></i>
                    <span class="sidebar-item-text">Gem manager</span>
                </li>
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <i class="fas fa-question-circle text-gray-400"></i>
                    <span class="sidebar-item-text">Help</span>
                    <span class="dot-indicator"></span>
                </li>
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <i class="fas fa-history text-gray-400"></i>
                    <span class="sidebar-item-text">Activity</span>
                </li>
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <i class="fas fa-cog text-gray-400"></i>
                    <span class="sidebar-item-text">Settings</span>
                    <span class="dot-indicator"></span>
                </li>
                <li class="flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800">
                    <a href="/admin.html" class="flex items-center gap-2 w-full">
                        <i class="fas fa-user-cog text-gray-400"></i>
                        <span class="sidebar-item-text">Admin Panel</span>
                    </a>
                </li>
            </ul>

            <div class="mt-auto text-xs text-gray-500 px-2 pt-4">
                <div class="flex items-center">
                    <span class="inline-block w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                    <span>Jan Phyl Village, FL, USA</span>
                </div>
                <div class="text-xs text-gray-500 mt-1">
                    <span class="text-gray-500">From your IP address · </span>
                    <a href="#" class="text-blue-400 hover:underline">Update location</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col">
        <!-- Desktop Header -->
        <div class="desktop-header chat-header justify-between items-center px-4 py-2 border-b border-gray-800">
            <div class="flex items-center">
                <div class="font-bold text-lg text-white mr-1">Varjis</div>
                <i class="fas fa-chevron-down text-xs text-gray-400 mr-2"></i>
                <div class="text-gray-300 text-sm">2.0 Lite</div>
            </div>
            <div class="flex items-center space-x-4">
                <button class="flex items-center bg-gray-800 hover:bg-gray-700 text-white rounded-full px-4 py-2 text-sm">
                    <span class="text-pink-500 mr-2">✦</span>
                    Try Varjis Advanced
                </button>
                <div class="p-2 cursor-pointer">
                    <i class="far fa-folder text-gray-400"></i>
                </div>
                <div class="p-2 cursor-pointer">
                    <i class="fas fa-th text-gray-400"></i>
                </div>
                <div class="w-8 h-8 rounded-full bg-purple-700 flex items-center justify-center">
                    O
                </div>
            </div>
        </div>

        <!-- Mobile Header -->
        <header class="mobile-header chat-header justify-between items-center px-4 py-2 border-b border-gray-800">
            <div class="flex items-center">
                <button class="p-2 mr-2" id="mobileMenuToggle">
                    <i class="fas fa-bars text-gray-400"></i>
                </button>
                <div class="flex items-center">
                    <span class="font-bold text-lg">Varjis</span>
                    <i class="fas fa-chevron-down text-xs text-gray-400 ml-1"></i>
                </div>
            </div>
            <div class="text-sm text-gray-400">2.0 Lite</div>
            <div class="flex items-center space-x-3">
                <button class="p-2">
                    <i class="fas fa-plus text-gray-400"></i>
                </button>
                <button class="p-2">
                    <i class="fas fa-ellipsis-v text-gray-400"></i>
                </button>
                <div class="w-8 h-8 rounded-full bg-purple-700 flex items-center justify-center">
                    O
                </div>
            </div>
        </header>

        <!-- Chat Area -->
        <div id="chatDisplay" class="flex-1 overflow-y-auto px-4 py-4">
            <!-- Messages will be added here dynamically -->
        </div>

        <!-- Input Box -->
        <div class="p-4 border-t border-gray-800">

            <!-- Document Preview Area -->
            <div id="documentPreviewContainer" class="mx-auto max-w-3xl mb-2 hidden">
                <!-- Document preview will be dynamically added here by JS -->
            </div>
            <button id="clearDocumentButton" class="w-full max-w-3xl mx-auto mt-1 mb-2 text-xs text-red-400 hover:text-red-300 hidden">&times; Clear Document</button>

            <!-- Image Preview Area - Updated for multiple images -->
            <div id="imagePreviewContainer" class="mx-auto max-w-3xl mb-2 hidden flex flex-wrap justify-center items-center gap-2">
                <!-- Image previews will be dynamically added here by JS -->
            </div>
            <button id="clearImagesButton" class="w-full max-w-3xl mx-auto mt-1 mb-2 text-xs text-red-400 hover:text-red-300 hidden">&times; Clear All Images</button>

            <!-- Input container -->
            <div class="mx-auto max-w-3xl input-container border border-gray-700">
                <div class="flex items-center px-4 py-3">
                    <!-- Image Upload Button -->
                    <label for="imageUpload" class="p-2 text-gray-400 hover:text-gray-300 cursor-pointer mr-1">
                        <i class="fas fa-camera"></i> <!-- Changed to camera icon for clarity -->
                    </label>
                    <input type="file" id="imageUpload" accept="image/*" class="hidden" multiple>

                    <!-- Document Upload Button -->
                    <label for="documentUpload" class="p-2 text-gray-400 hover:text-gray-300 cursor-pointer mr-2">
                        <i class="fas fa-file-alt"></i> <!-- Document icon -->
                    </label>
                    <input type="file" id="documentUpload" accept=".pdf,.js,.py,.txt,.html,.css,.md,.csv,.xml,.rtf" class="hidden">

                    <!-- Main text input -->
                    <input id="textInput" type="text" placeholder="Ask Varjis, attach images or documents" class="flex-1 bg-transparent text-white outline-none px-2">

                    <!-- Send and record buttons -->
                    <div class="flex items-center">
                        <button id="sendButton" class="p-2 text-gray-400 hover:text-gray-300">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        <div class="relative">
                            <button id="recordButton" class="p-2 text-gray-400 hover:text-gray-300">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <div id="recordingIndicator" class="recording-indicator hidden">Recording...</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-3xl mx-auto mt-2 text-center">
                <p class="text-gray-500 text-sm">Varjis can make mistakes, so double-check it</p>
            </div>
        </div>
    </div>

    <!-- Main script -->
     <script src="/js/main.js"></script>
    <!-- Time display scripts -->
    <script src="/js/time-display.js"></script>
</body>
</html>

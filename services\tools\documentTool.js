import { GoogleGenAI, createPartFromUri } from "@google/genai";
import fs from "fs";
import dotenv from "dotenv";
dotenv.config();

const API_KEY = process.env.GEMINI_API_KEY;

/**
 * Document processing tool for Gemini
 * Handles PDF documents with support for large files
 * @param {string} input - Command string with action and parameters
 * @returns {Promise<string>} - Processing results
 */
export const documentTool = async (input) => {
  try {
    // Parse input
    const match = input.match(/^(\w+)\s+"([^"]+)"(?:\s+"([^"]+)")?(?:\s+(.+))?$/);
    if (!match) {
      return getHelpText();
    }

    const [_, action, path, secondParam, optionsStr] = match;
    const options = parseOptions(optionsStr || "");

    // Initialize Google Gemini AI
    const ai = new GoogleGenAI({ apiKey: API_KEY });

    switch (action) {
      case "analyze":
        return await analyzeDocument(ai, path, options.prompt || "Analyze this document in detail.");
      case "summarize":
        return await analyzeDocument(ai, path, options.prompt || "Summarize this document concisely.");
      case "extract":
        return await analyzeDocument(ai, path, options.prompt || "Extract key information from this document.");
      case "compare":
        if (!secondParam) {
          return "Error: Second document path required for comparison.";
        }
        return await compareDocuments(ai, path, secondParam, options);
      case "help":
        return getHelpText();
      default:
        return `Unknown action: ${action}. Try 'help' for available actions.`;
    }
  } catch (error) {
    console.error("Error in document tool:", error);
    return `Error processing document: ${error.message}`;
  }
};

/**
 * Analyze a document with Gemini
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} docPath - Path to the document
 * @param {string} prompt - Custom prompt for analysis
 * @returns {Promise<string>} - Analysis result
 */
async function analyzeDocument(ai, docPath, prompt) {
  try {
    // Check if file exists
    if (!fs.existsSync(docPath)) {
      return `Error: File not found at path ${docPath}`;
    }

    // Get file size in MB
    const stats = fs.statSync(docPath);
    const fileSizeInMB = stats.size / (1024 * 1024);
    const mimeType = getMimeType(docPath);

    if (!mimeType) {
      const ext = docPath.split('.').pop().toLowerCase();
      return `Error: File type ".${ext}" is not supported by the document tool. Please use one of the supported types (see help).`;
    }

    let response;

    // For large files (>20MB), use the File API
    if (fileSizeInMB > 20) {
      const file = await ai.files.upload({
        file: docPath,
        config: { mimeType }
      });

      // Wait for the file to be processed
      let getFile = await ai.files.get({ name: file.name });
      while (getFile.state === 'PROCESSING') {
        getFile = await ai.files.get({ name: file.name });
        console.log(`Current file status: ${getFile.state}`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
      
      if (getFile.state === 'FAILED') {
        throw new Error('File processing failed.');
      }

      // Create content array
      const content = [prompt];
      
      if (file.uri && file.mimeType) {
        const fileContent = createPartFromUri(file.uri, file.mimeType);
        content.push(fileContent);
      }

      response = await ai.models.generateContent({
        model: "gemini-2.0-flash",
        contents: content
      });
    } else {
      // For smaller files, use inline data
      const base64File = fs.readFileSync(docPath, { encoding: "base64" });
      
      response = await ai.models.generateContent({
        model: "gemini-2.0-flash",
        contents: [
          { text: prompt },
          {
            inlineData: {
              mimeType: mimeType,
              data: base64File
            }
          }
        ]
      });
    }

    return response.text;
  } catch (error) {
    console.error("Error analyzing document:", error);
    throw error;
  }
}

/**
 * Compare two documents
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} docPath1 - Path to first document
 * @param {string} docPath2 - Path to second document
 * @param {object} options - Comparison options
 * @returns {Promise<string>} - Comparison result
 */
async function compareDocuments(ai, docPath1, docPath2, options) {
  try {
    // Check if files exist
    if (!fs.existsSync(docPath1)) {
      return `Error: First file not found at path ${docPath1}`;
    }
    if (!fs.existsSync(docPath2)) {
      return `Error: Second file not found at path ${docPath2}`;
    }

    // Upload both documents
    const file1 = await uploadDocument(ai, docPath1);
    const file2 = await uploadDocument(ai, docPath2);

    // Create prompt based on options or use default
    const prompt = options.prompt || 
      "Compare these two documents. Identify key similarities and differences.";

    // Create content array with prompt and both documents
    const content = [prompt];
    
    if (file1.uri && file1.mimeType) {
      content.push(createPartFromUri(file1.uri, file1.mimeType));
    }
    
    if (file2.uri && file2.mimeType) {
      content.push(createPartFromUri(file2.uri, file2.mimeType));
    }

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: content
    });

    return response.text;
  } catch (error) {
    console.error("Error comparing documents:", error);
    throw error;
  }
}

/**
 * Upload a document and wait for processing
 * @param {GoogleGenAI} ai - Gemini AI instance
 * @param {string} docPath - Path to document
 * @returns {Promise<object>} - Uploaded file object
 */
async function uploadDocument(ai, docPath) {
  const mimeType = getMimeType(docPath);

  if (!mimeType) {
    const ext = docPath.split('.').pop().toLowerCase();
    // This error will be caught by the calling function (compareDocuments)
    // and then by the main documentTool error handler.
    throw new Error(`File type ".${ext}" is not supported for document comparison. Please use one of the supported types (see help).`);
  }
  
  const file = await ai.files.upload({
    file: docPath,
    config: { 
      mimeType,
      displayName: docPath.split('/').pop()
    }
  });

  // Wait for the file to be processed
  let getFile = await ai.files.get({ name: file.name });
  while (getFile.state === 'PROCESSING') {
    getFile = await ai.files.get({ name: file.name });
    console.log(`Current file status: ${getFile.state}`);
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  if (getFile.state === 'FAILED') {
    throw new Error('File processing failed.');
  }
  
  return getFile;
}

/**
 * Get MIME type based on file extension
 * @param {string} filePath - Path to file
 * @returns {string} - MIME type
 */
function getMimeType(filePath) {
  const ext = filePath.split('.').pop().toLowerCase();
  // Supported MIME types by Gemini 1.5 Pro/Flash for document processing
  const mimeTypes = {
    'pdf': 'application/pdf',
    'js': 'application/x-javascript', // or text/javascript
    'py': 'application/x-python',    // or text/x-python
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'md': 'text/md',
    'csv': 'text/csv',
    'xml': 'text/xml',
    'rtf': 'text/rtf'
    // Note: text/javascript and text/x-python are also valid but map to same extensions here.
  };
  
  return mimeTypes[ext] || null; // Return null if not explicitly supported
}

/**
 * Parse options string into object
 * @param {string} optionsStr - Options string
 * @returns {object} - Options object
 */
function parseOptions(optionsStr) {
  const options = {};
  
  if (!optionsStr) return options;
  
  // Handle JSON format
  if (optionsStr.startsWith('{') && optionsStr.endsWith('}')) {
    try {
      return JSON.parse(optionsStr);
    } catch (e) {
      console.error("Error parsing JSON options:", e);
    }
  }
  
  // Handle key:value format
  const optionPairs = optionsStr.split(' ');
  optionPairs.forEach(pair => {
    const [key, value] = pair.split(':');
    if (key && value) {
      options[key] = value;
    }
  });
  
  return options;
}

/**
 * Returns help text for the document tool
 * @returns {string} Help text
 */
function getHelpText() {
  return `
Document Tool Help:

This tool allows you to analyze and process PDF documents using Google's Gemini API.

Available actions:
- analyze: Get a detailed analysis of a document
  Example: [TOOL: document(analyze "path/to/document.pdf")]
  Options: prompt:"Custom analysis prompt"

- summarize: Get a concise summary of a document
  Example: [TOOL: document(summarize "path/to/document.pdf")]
  Options: prompt:"Custom summary prompt"

- extract: Extract specific information from a document
  Example: [TOOL: document(extract "path/to/document.pdf" prompt:"Extract all tables")]

- compare: Compare two documents
  Example: [TOOL: document(compare "path/to/doc1.pdf" "path/to/doc2.pdf")]
  Options: prompt:"Find differences in methodology"

- help: Show this help text
  Example: [TOOL: document(help)]

Supported document types (as per Gemini 1.5 Pro/Flash documentation):
- PDF (application/pdf)
- JavaScript (application/x-javascript, text/javascript)
- Python (application/x-python, text/x-python)
- TXT (text/plain)
- HTML (text/html)
- CSS (text/css)
- Markdown (text/md)
- CSV (text/csv)
- XML (text/xml)
- RTF (text/rtf)

Note: For files larger than 20MB (and supported), the tool automatically uses Gemini's File API.
Unsupported file types will result in an error.
`;
}

import express from 'express';
import { processMessage } from '../services/agentLogic-simplified.js';
import { analyze } from '../services/nlu-service.js';
import Conversation from '../models/conversation.js';

const router = express.Router();

/**
 * POST /api/agent/chat
 * Process a message with the AI agent
 */
router.post('/chat', async (req, res) => {
  try {
    const { message, context } = req.body;
    const sessionId = req.body.sessionId || 'default-session';

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Check if this is a code-related request
    const isCodeRequest = message.toLowerCase().includes('write') &&
      (message.toLowerCase().includes('code') ||
       message.toLowerCase().includes('html') ||
       message.toLowerCase().includes('css') ||
       message.toLowerCase().includes('javascript') ||
       message.toLowerCase().includes('python') ||
       message.toLowerCase().includes('function'));

    // Make sure context includes generateSpeech and sessionId
    const updatedContext = {
      ...context, // context from req.body might include uploadedDocumentPath, uploadedDocumentName
      generateSpeech: true,
      sessionId: sessionId,
      isCodeRequest: isCodeRequest
    };

    console.log(`Processing chat message for session: ${sessionId}`);
    if (updatedContext.uploadedDocumentPath) {
      console.log(`Chat context includes uploaded document: ${updatedContext.uploadedDocumentName} at ${updatedContext.uploadedDocumentPath}`);
    }

    // Analyze the message with NLU engine first
    console.log('Analyzing message with NLU engine');
    const nluAnalysis = await analyze(message);

    // Add NLU analysis to context if available
    if (nluAnalysis) {
      console.log('NLU analysis:', JSON.stringify(nluAnalysis, null, 2));
      updatedContext.nluAnalysis = nluAnalysis;
    } else {
      console.log('NLU analysis failed or returned null');
    }

    // Process the message with the agent
    const response = await processMessage(message, updatedContext);

    // Add sessionId to the response
    response.sessionId = sessionId;

    // Handle audio buffer or browser TTS instructions
    if (response.audioBuffer) {
      // Check if it's browser TTS instructions
      if (typeof response.audioBuffer === 'object' && response.audioBuffer.type === 'browser-tts') {
        console.log('Received browser TTS instructions for chat response');
        response.browserTTS = response.audioBuffer;
        delete response.audioBuffer;
      }
      // Check if it's a proper audio buffer
      else if (Buffer.isBuffer(response.audioBuffer)) {
        console.log('Converting audio buffer to data URL for chat response, buffer size:', response.audioBuffer.length);

        // Create a proper base64 encoding of the audio buffer
        const base64Audio = response.audioBuffer.toString('base64');
        response.audioUrl = `data:audio/mp3;base64,${base64Audio}`;
        console.log('Data URL created, length:', response.audioUrl.length);

        // Remove the buffer from the response to reduce payload size
        delete response.audioBuffer;
      }
      else {
        console.warn('Unexpected audioBuffer type:', typeof response.audioBuffer);
        delete response.audioBuffer;
      }
    }

    // Return the response with NLU analysis
    return res.json({
      ...response,
      nluAnalysis: updatedContext.nluAnalysis || null
    });
  } catch (error) {
    console.error('Error processing message with agent:', error);
    return res.status(500).json({ error: 'Error processing message with agent' });
  }
});

/**
 * POST /api/agent/voice
 * Process a voice message with the AI agent and return a voice response
 */
router.post('/voice', async (req, res) => {
  try {
    const { transcription, context } = req.body;
    const sessionId = req.body.sessionId || 'default-session';

    if (!transcription) {
      return res.status(400).json({ error: 'Transcription is required' });
    }

    // Make sure context includes generateSpeech and sessionId
    const updatedContext = {
      ...context,
      generateSpeech: true,
      sessionId: sessionId
    };

    // Process the transcribed message with the agent
    const response = await processMessage(transcription, updatedContext);

    // Add sessionId to the response
    response.sessionId = sessionId;

    // Handle audio buffer or browser TTS instructions
    if (response.audioBuffer) {
      // Check if it's browser TTS instructions
      if (typeof response.audioBuffer === 'object' && response.audioBuffer.type === 'browser-tts') {
        console.log('Received browser TTS instructions for voice response');
        response.browserTTS = response.audioBuffer;
        delete response.audioBuffer;
      }
      // Check if it's a proper audio buffer
      else if (Buffer.isBuffer(response.audioBuffer)) {
        console.log('Converting audio buffer to data URL, buffer size:', response.audioBuffer.length);

        // Create a proper base64 encoding of the audio buffer
        const base64Audio = response.audioBuffer.toString('base64');
        response.audioUrl = `data:audio/mp3;base64,${base64Audio}`;
        console.log('Data URL created, length:', response.audioUrl.length);

        // Remove the buffer from the response to reduce payload size
        delete response.audioBuffer;
      }
      else {
        console.warn('Unexpected audioBuffer type in voice response:', typeof response.audioBuffer);
        delete response.audioBuffer;
      }
    }

    // Return the response with NLU analysis
    return res.json({
      ...response,
      nluAnalysis: updatedContext.nluAnalysis || null
    });
  } catch (error) {
    console.error('Error processing voice message with agent:', error);
    return res.status(500).json({ error: 'Error processing voice message with agent' });
  }
});

/**
 * GET /api/agent/memory
 * Get the current conversation memory
 */
router.get('/memory', async (req, res) => {
  try {
    const sessionId = req.query.sessionId || 'default-session';

    // Access the memory from agentLogic with the specified sessionId
    const memory = await processMessage('__get_memory__', {
      getMemoryOnly: true,
      sessionId: sessionId
    });

    return res.json(memory);
  } catch (error) {
    console.error('Error retrieving conversation memory:', error);
    return res.status(500).json({ error: 'Error retrieving conversation memory' });
  }
});

/**
 * DELETE /api/agent/memory
 * Clear the conversation memory
 */
router.delete('/memory', async (req, res) => {
  try {
    const sessionId = req.query.sessionId || req.body.sessionId || 'default-session';

    // Clear the memory for the specified sessionId
    const result = await processMessage('__clear_memory__', {
      clearMemory: true,
      sessionId: sessionId
    });

    return res.json({
      success: true,
      message: `Conversation memory cleared for session ${sessionId}`,
      sessionId: sessionId
    });
  } catch (error) {
    console.error('Error clearing conversation memory:', error);
    return res.status(500).json({ error: 'Error clearing conversation memory' });
  }
});

/**
 * GET /api/agent/conversations
 * Get all conversation sessions
 */
router.get('/conversations', async (req, res) => {
  try {
    // Get all conversations from MongoDB, sorted by lastUpdated
    const conversations = await Conversation.find({}).sort({ lastUpdated: -1 }).limit(20); // Limit to 20 most recent conversations

    // Format the conversations for the client
    const formattedConversations = conversations.map(conv => {
      // Find the first user message to use as title
      let title = 'New conversation';

      if (conv.messages && conv.messages.length > 0) {
        // Try to find the first user message
        const userMessage = conv.messages.find(msg => msg.role === 'user');
        if (userMessage) {
          title = userMessage.text;
        } else {
          // If no user message, use the first message regardless of role
          title = conv.messages[0].text;
        }

        // Truncate the title if it's too long
        if (title.length > 30) {
          title = title.substring(0, 27) + '...';
        }
      }

      return {
        id: conv._id,
        sessionId: conv.sessionId,
        title: title,
        lastUpdated: conv.lastUpdated,
        messageCount: conv.messages ? conv.messages.length : 0
      };
    });

    return res.json({
      conversations: formattedConversations
    });
  } catch (error) {
    console.error('Error retrieving conversations:', error);
    return res.status(500).json({
      error: 'Error retrieving conversations',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

export default router;

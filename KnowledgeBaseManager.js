import dotenv from 'dotenv';
dotenv.config();

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { Pinecone } from '@pinecone-database/pinecone';
import { generateEmbedding } from './services/embeddingService.js'; // Corrected path

// use config


// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Pinecone Configuration
const PINECONE_API_KEY = process.env.PINECONE_API_KEY || "YOUR_PINECONE_API_KEY"; // Replace with your API key or set as env var
// const PINECONE_ENVIRONMENT = process.env.PINECONE_ENVIRONMENT || "YOUR_PINECONE_ENVIRONMENT"; // Environment is often part of the API key or not needed for new client versions
const PINECONE_INDEX_NAME = 'kb'; // Changed to match your existing serverless index
const EMBEDDING_DIMENSION = 768; 

let pinecone;
let pineconeIndex;

async function initPinecone() {
    if (pineconeIndex) {
        return;
    }
    if (!PINECONE_API_KEY || PINECONE_API_KEY === "YOUR_PINECONE_API_KEY") {
        console.error("Pinecone API Key is not configured. Please set PINECONE_API_KEY environment variable or update the placeholder in the code.");
        // Potentially throw an error or disable Pinecone functionality
        return;
    }
    // if (!PINECONE_ENVIRONMENT || PINECONE_ENVIRONMENT === "YOUR_PINECONE_ENVIRONMENT") { // Check removed as environment is not used in constructor
    //     console.error("Pinecone Environment is not configured. Please set PINECONE_ENVIRONMENT environment variable or update the placeholder in the code.");
    //     // Potentially throw an error or disable Pinecone functionality
    //     return;
    // }

    try {
        pinecone = new Pinecone({
            apiKey: PINECONE_API_KEY,
            // environment: PINECONE_ENVIRONMENT, // Removed based on error
        });

        const indexList = await pinecone.listIndexes();
        if (!indexList.indexes || !indexList.indexes.some(index => index.name === PINECONE_INDEX_NAME)) {
            console.log(`Pinecone index "${PINECONE_INDEX_NAME}" not found. Creating new index...`);
            await pinecone.createIndex({
                name: PINECONE_INDEX_NAME,
                dimension: EMBEDDING_DIMENSION,
                metric: 'cosine', // or 'dotproduct', 'euclidean'
                spec: {
                  serverless: {
                    cloud: 'aws',
                    region: 'us-east-1'
                  }
                }
            });
            console.log(`Pinecone index "${PINECONE_INDEX_NAME}" created successfully with serverless spec. It may take a few moments to initialize.`);
             // It might take a moment for the index to be ready.
            await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        } else {
            console.log(`Pinecone index "${PINECONE_INDEX_NAME}" found.`);
        }
        pineconeIndex = pinecone.Index(PINECONE_INDEX_NAME);
        console.log(`Connected to Pinecone index "${PINECONE_INDEX_NAME}".`);
    } catch (error) {
        console.error('Error initializing Pinecone:', error);
        // Decide how to handle this: throw error, or allow app to run without Pinecone
        throw error;
    }
}


const BASE_DIR = path.join(__dirname, 'knowledge_base');
const CONFIG_DIR = path.join(BASE_DIR, 'configurations');
const NOTES_DIR = path.join(BASE_DIR, 'notes');
const SNIPPETS_DIR = path.join(BASE_DIR, 'snippets');

/**
 * Ensures that the base directories for the knowledge base exist.
 */
async function ensureBaseDirectories() {
    try {
        await fs.mkdir(BASE_DIR, { recursive: true });
        await fs.mkdir(CONFIG_DIR, { recursive: true });
        await fs.mkdir(NOTES_DIR, { recursive: true });
        await fs.mkdir(SNIPPETS_DIR, { recursive: true });
        // console.log('Knowledge base directories ensured.');
        await initPinecone(); // Initialize Pinecone when ensuring directories
    } catch (error) {
        console.error('Error ensuring knowledge base directories or initializing Pinecone:', error);
        throw error; // Re-throw to indicate failure
    }
}

/**
 * Adds an entry to the knowledge base.
 * @param {string} type - "configuration", "note", or "snippet".
 * @param {string} name - The filename (e.g., "server_settings.json").
 * @param {string} content - The content of the file.
 * @param {object} [metadata=null] - Optional metadata. If provided for snippets,
 *                                   a .meta.json file will be created.
 * @returns {Promise<string>} Path to the created file.
 */
async function addEntry(type, name, content, metadata = null) {
    // Implementation to come
    await ensureBaseDirectories(); // Ensure directories exist before writing
    let targetDir;
    let filePath;

    switch (type) {
        case 'configuration':
            targetDir = CONFIG_DIR;
            break;
        case 'note':
            targetDir = NOTES_DIR;
            break;
        case 'snippet':
            targetDir = SNIPPETS_DIR;
            break;
        default:
            throw new Error(`Invalid entry type: ${type}. Must be 'configuration', 'note', or 'snippet'.`);
    }

    filePath = path.join(targetDir, name);
    await fs.writeFile(filePath, content, 'utf8');
    console.log(`Entry created: ${filePath}`);

    if (type === 'snippet' && metadata) {
        const metaFilePath = `${filePath}.meta.json`;
        await fs.writeFile(metaFilePath, JSON.stringify(metadata, null, 2), 'utf8');
        console.log(`Metadata file created: ${metaFilePath}`);
    }

    // Add to Pinecone
    if (pineconeIndex) {
        try {
            const embedding = await generateEmbedding(content);
            const pineconeId = `${type}-${name}`; // Unique ID for Pinecone
            
            // For snippets, include their specific metadata. For others, just basic info.
            let pineconeMetadata = {
                type,
                name,
                fullContent: content, // Storing full content for now, consider alternatives for large files
            };

            if (type === 'snippet' && metadata) {
                // Flatten snippet metadata for Pinecone
                if (typeof metadata.description === 'string') {
                    pineconeMetadata.snippet_description = metadata.description;
                }
                if (Array.isArray(metadata.tags) && metadata.tags.every(tag => typeof tag === 'string')) {
                    pineconeMetadata.snippet_tags = metadata.tags;
                }
                // Example for another common field, adapt as needed
                if (Array.isArray(metadata.related_configs) && metadata.related_configs.every(rc => typeof rc === 'string')) {
                    pineconeMetadata.snippet_related_configs = metadata.related_configs;
                }
                // Add any other known, flat-able snippet metadata fields here
            }

            await pineconeIndex.upsert([{
                id: pineconeId,
                values: embedding,
                metadata: pineconeMetadata
            }]);
            console.log(`Entry "${pineconeId}" upserted to Pinecone.`);
        } catch (error) {
            console.error(`Error upserting entry "${type}-${name}" to Pinecone:`, error);
            // Decide if this error should be fatal or just a warning
        }
    }

    return filePath;
}

/**
 * Retrieves an entry from the knowledge base.
 * @param {string} type - "configuration", "note", or "snippet".
 * @param {string} name - The filename.
 * @returns {Promise<{content: string, metadata?: object}>} The entry content and optional metadata.
 */
async function getEntry(type, name) {
    // Implementation to come
    await ensureBaseDirectories();
    let targetDir;
    let filePath;
    let metaFilePath;

    switch (type) {
        case 'configuration':
            targetDir = CONFIG_DIR;
            break;
        case 'note':
            targetDir = NOTES_DIR;
            break;
        case 'snippet':
            targetDir = SNIPPETS_DIR;
            break;
        default:
            throw new Error(`Invalid entry type: ${type}. Must be 'configuration', 'note', or 'snippet'.`);
    }
    
    filePath = path.join(targetDir, name);
    const content = await fs.readFile(filePath, 'utf8');
    let entry = { content };

    if (type === 'snippet') {
        metaFilePath = `${filePath}.meta.json`;
        try {
            const metaContent = await fs.readFile(metaFilePath, 'utf8');
            entry.metadata = JSON.parse(metaContent);
        } catch (error) {
            // If meta file doesn't exist or is unreadable, proceed without it
            if (error.code !== 'ENOENT') {
                console.warn(`Could not read metadata for ${name}:`, error.message);
            }
        }
    }
    return entry;
}

/**
 * Lists entries in the knowledge base.
 * @param {string} [type=null] - Optional. Filter by "configuration", "note", or "snippet".
 * @returns {Promise<Array<string>>} A list of entry names (filenames).
 */
async function listEntries(type = null) {
    // Implementation to come
    await ensureBaseDirectories();
    const dirsToList = [];
    if (!type) {
        dirsToList.push(CONFIG_DIR, NOTES_DIR, SNIPPETS_DIR);
    } else {
        switch (type) {
            case 'configuration':
                dirsToList.push(CONFIG_DIR);
                break;
            case 'note':
                dirsToList.push(NOTES_DIR);
                break;
            case 'snippet':
                dirsToList.push(SNIPPETS_DIR);
                break;
            default:
                throw new Error(`Invalid entry type: ${type}. Must be 'configuration', 'note', or 'snippet'.`);
        }
    }

    let allEntries = [];
    for (const dir of dirsToList) {
        try {
            const files = await fs.readdir(dir);
            // Filter out .meta.json files from the main list if listing snippets
            const filteredFiles = files.filter(file => !(dir === SNIPPETS_DIR && file.endsWith('.meta.json')));
            // Store full path for later use in search or direct access, but return relative path for listing
            allEntries = allEntries.concat(filteredFiles.map(file => path.join(path.relative(BASE_DIR, dir), file).replace(/\\/g, '/')));
        } catch (error) {
            if (error.code === 'ENOENT') {
                // Directory might not exist if no entries of that type yet, which is fine.
                console.log(`Directory not found or empty, skipping: ${dir}`);
            } else {
                console.error(`Error reading directory ${dir}:`, error);
                throw error;
            }
        }
    }
    return allEntries;
}

/**
 * Searches entries in the knowledge base.
 * @param {string} keywords - Space-separated keywords.
 * @param {string} [type=null] - Optional. Filter by "configuration", "note", or "snippet".
 * @returns {Promise<Array<{name: string, type: string, relativePath: string, context?: string}>>} A list of matching entries.
 */
async function searchEntries(keywords, searchType = null) {
    await ensureBaseDirectories(); // This will also initPinecone()

    // Pinecone search (if available and keywords are provided)
    if (pineconeIndex && keywords && keywords.trim().length > 0) {
        try {
            console.log(`Searching Pinecone for keywords: "${keywords}" with type: ${searchType || 'all'}`);
            const keywordEmbedding = await generateEmbedding(keywords);
            const queryResponse = await pineconeIndex.query({
                vector: keywordEmbedding,
                topK: 10, // Number of results to fetch
                includeMetadata: true,
                filter: searchType ? { "type": searchType } : undefined // Enabled server-side filter
            });

            const pineconeResults = [];
            if (queryResponse.matches) {
                for (const match of queryResponse.matches) {
                    // Ensure essential metadata fields 'type' and 'name' exist and are strings
                    if (match.metadata && typeof match.metadata.type === 'string' && typeof match.metadata.name === 'string') {
                        
                        // Optional: If server-side filter might not be exhaustive or if an additional client-side check is desired.
                        // if (searchType && match.metadata.type !== searchType) {
                        //     continue; 
                        // }

                        let dirName;
                        switch (match.metadata.type) {
                            case 'configuration':
                                dirName = 'configurations';
                                break;
                            case 'note':
                                dirName = 'notes';
                                break;
                            case 'snippet':
                                dirName = 'snippets';
                                break;
                            default:
                                // This case implies match.metadata.type was a string but not one of the expected.
                                console.warn(`Unexpected metadata.type string value: "${match.metadata.type}" for ID ${match.id}. Using type as directory name.`);
                                dirName = match.metadata.type; 
                        }
                        const relativePath = `${dirName}/${match.metadata.name}`;
                        
                        let reconstructedSnippetMetadata = {};
                        if (match.metadata.type === 'snippet') {
                            if (typeof match.metadata.snippet_description === 'string') {
                                reconstructedSnippetMetadata.description = match.metadata.snippet_description;
                            }
                            if (Array.isArray(match.metadata.snippet_tags)) {
                                reconstructedSnippetMetadata.tags = match.metadata.snippet_tags;
                            }
                            if (Array.isArray(match.metadata.snippet_related_configs)) {
                                reconstructedSnippetMetadata.related_configs = match.metadata.snippet_related_configs;
                            }
                            // Reconstruct other flattened fields here
                        }

                        pineconeResults.push({
                            name: match.metadata.name,
                            type: match.metadata.type,
                            relativePath: relativePath,
                            context: match.metadata.fullContent ? (match.metadata.fullContent.substring(0, 200) + (match.metadata.fullContent.length > 200 ? '...' : '')) : 'No content preview.',
                            score: match.score,
                            ...(Object.keys(reconstructedSnippetMetadata).length > 0 ? { metadata: reconstructedSnippetMetadata } : {})
                        });
                    } else {
                        // Log and skip matches with missing or improperly typed 'type' or 'name'
                        console.warn(`Skipping Pinecone match due to missing or invalid 'type' or 'name' in metadata. ID: ${match.id}, Score: ${match.score}, Metadata:`, JSON.stringify(match.metadata, null, 2));
                    }
                }
            }
            console.log(`Found ${pineconeResults.length} results from Pinecone.`);
            if (pineconeResults.length > 0) {
                return pineconeResults; // Prioritize Pinecone results if available
            }
        } catch (error) {
            console.error("Error searching Pinecone:", error);
            // Fallback to filesystem search if Pinecone search fails
        }
    }

    // Fallback to existing filesystem search if Pinecone is not used or returns no results
    console.log("Falling back to filesystem search.");
    const searchTerms = keywords.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    if (searchTerms.length === 0) {
        return [];
    }

    const results = [];
    const entryTypesToSearch = [];

    if (!searchType) {
        entryTypesToSearch.push(
            { type: 'configuration', dir: CONFIG_DIR },
            { type: 'note', dir: NOTES_DIR },
            { type: 'snippet', dir: SNIPPETS_DIR }
        );
    } else {
        switch (searchType) {
            case 'configuration':
                entryTypesToSearch.push({ type: 'configuration', dir: CONFIG_DIR });
                break;
            case 'note':
                entryTypesToSearch.push({ type: 'note', dir: NOTES_DIR });
                break;
            case 'snippet':
                entryTypesToSearch.push({ type: 'snippet', dir: SNIPPETS_DIR });
                break;
            default:
                throw new Error(`Invalid search type: ${searchType}. Must be 'configuration', 'note', or 'snippet'.`);
        }
    }

    for (const { type: currentEntryType, dir } of entryTypesToSearch) {
        try {
            const files = await fs.readdir(dir);
            for (const file of files) {
                // Skip metadata files in snippets directory during general search pass
                if (currentEntryType === 'snippet' && file.endsWith('.meta.json')) {
                    continue;
                }

                const filePath = path.join(dir, file);
                const relativePath = path.join(path.relative(BASE_DIR, dir), file).replace(/\\/g, '/');
                let content = '';
                try {
                    content = await fs.readFile(filePath, 'utf8');
                } catch (readError) {
                    console.warn(`Could not read file ${filePath} during search: ${readError.message}`);
                    continue; // Skip unreadable files
                }

                const searchableContent = `${file.toLowerCase()} ${content.toLowerCase()}`;
                let matchFound = false;
                for (const term of searchTerms) {
                    if (searchableContent.includes(term)) {
                        matchFound = true;
                        break;
                    }
                }

                if (matchFound) {
                    // Basic context: first 100 chars of content. Could be improved.
                    const context = content.substring(0, 100) + (content.length > 100 ? '...' : '');
                    results.push({
                        name: file,
                        type: currentEntryType,
                        relativePath: relativePath,
                        context: context
                    });
                }
            }
        } catch (error) {
            if (error.code === 'ENOENT') {
                // Directory might not exist, skip.
            } else {
                console.error(`Error searching in directory ${dir}:`, error);
            }
        }
    }
    return results;
}

// Export individual functions for ES modules
export {
    addEntry,
    getEntry,
    listEntries,
    searchEntries,
    ensureBaseDirectories
};

// Example Usage (for testing - can be removed or commented out later)
/*
async function testKnowledgeBase() {
    try {
        await ensureBaseDirectories(); // Call this once at the start if not using other functions that call it

        // Add a configuration
        await addEntry('configuration', 'server.json', JSON.stringify({ host: 'localhost', port: 3000 }, null, 2));

        // Add a note
        await addEntry('note', 'meeting_summary.md', '# Meeting Notes\n- Discussed project X\n- Action item: Follow up with team Y');

        // Add a snippet with metadata
        const snippetContent = "console.log('This is a test snippet');";
        const snippetMetadata = { description: "A simple test snippet", related_configs: ["configurations/server.json"], tags: ["test", "example"] };
        await addEntry('snippet', 'test_snippet.js', snippetContent, snippetMetadata);

        // List all entries
        console.log('\nAll Entries:');
        const allEntries = await listEntries();
        console.log(allEntries);

        // List only snippets
        console.log('\nSnippets:');
        const snippets = await listEntries('snippet');
        console.log(snippets);

        // Get a specific entry
        console.log('\nGetting server.json:');
        const serverConfig = await getEntry('configuration', 'server.json');
        console.log(serverConfig.content);

        console.log('\nGetting test_snippet.js:');
        const snippetEntry = await getEntry('snippet', 'test_snippet.js');
        console.log('Content:', snippetEntry.content);
        console.log('Metadata:', snippetEntry.metadata);

        // Search for "localhost" in configurations
        console.log('\nSearching for "localhost" in configurations:');
        let searchResults = await searchEntries('localhost', 'configuration');
        console.log(searchResults);

        // Search for "project X" in notes
        console.log('\nSearching for "project X" in notes:');
        searchResults = await searchEntries('project X', 'note');
        console.log(searchResults);
        
        // Search for "snippet" across all types
        console.log('\nSearching for "snippet" (all types):');
        searchResults = await searchEntries('snippet');
        console.log(searchResults);

        // Search for a non-existent term
        console.log('\nSearching for "nonexistentterm123":');
        searchResults = await searchEntries('nonexistentterm123');
        console.log(searchResults);


    } catch (error) {
        console.error('Error in testKnowledgeBase:', error);
    }
}

// If running this file directly:
// if (process.argv[1] === __filename) {
// (async () => {
// await testKnowledgeBase();
// })();
// }
*/

/**
 * Free Browser-based Text-to-Speech using Web Speech API
 * This provides client-side TTS functionality that works without any server costs
 */

class FreeTTS {
  constructor() {
    this.synthesis = window.speechSynthesis;
    this.voices = [];
    this.isSupported = 'speechSynthesis' in window;
    this.currentUtterance = null;
    
    if (this.isSupported) {
      this.loadVoices();
      // Voices might load asynchronously
      this.synthesis.addEventListener('voiceschanged', () => {
        this.loadVoices();
      });
    }
  }

  /**
   * Load available voices
   */
  loadVoices() {
    this.voices = this.synthesis.getVoices();
    console.log('Free TTS: Loaded voices:', this.voices.map(v => `${v.name} (${v.lang})`));
  }

  /**
   * Get the best voice for the given language
   * @param {string} lang - Language code (e.g., 'en-US')
   * @param {string} gender - Preferred gender ('male', 'female', or 'neutral')
   * @returns {SpeechSynthesisVoice|null} - The best matching voice
   */
  getBestVoice(lang = 'en-US', gender = 'neutral') {
    if (!this.voices.length) {
      this.loadVoices();
    }

    // First, try to find exact language match
    let candidates = this.voices.filter(voice => voice.lang === lang);
    
    // If no exact match, try language prefix (e.g., 'en' from 'en-US')
    if (candidates.length === 0) {
      const langPrefix = lang.split('-')[0];
      candidates = this.voices.filter(voice => voice.lang.startsWith(langPrefix));
    }
    
    // If still no match, use any available voice
    if (candidates.length === 0) {
      candidates = this.voices;
    }

    // Try to match gender preference
    if (gender === 'female') {
      const femaleVoices = candidates.filter(voice => 
        voice.name.toLowerCase().includes('female') || 
        voice.name.toLowerCase().includes('woman') ||
        voice.name.toLowerCase().includes('girl') ||
        voice.name.toLowerCase().includes('zira') ||
        voice.name.toLowerCase().includes('hazel')
      );
      if (femaleVoices.length > 0) return femaleVoices[0];
    } else if (gender === 'male') {
      const maleVoices = candidates.filter(voice => 
        voice.name.toLowerCase().includes('male') || 
        voice.name.toLowerCase().includes('man') ||
        voice.name.toLowerCase().includes('boy') ||
        voice.name.toLowerCase().includes('david') ||
        voice.name.toLowerCase().includes('mark')
      );
      if (maleVoices.length > 0) return maleVoices[0];
    }

    // Return first available voice
    return candidates.length > 0 ? candidates[0] : null;
  }

  /**
   * Speak the given text
   * @param {string} text - Text to speak
   * @param {object} options - Voice options
   * @returns {Promise} - Promise that resolves when speech is complete
   */
  speak(text, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('Speech synthesis not supported in this browser'));
        return;
      }

      if (!text || text.trim().length === 0) {
        resolve();
        return;
      }

      // Stop any current speech
      this.stop();

      // Create utterance
      const utterance = new SpeechSynthesisUtterance(text);
      this.currentUtterance = utterance;

      // Set voice
      const voice = this.getBestVoice(options.lang || 'en-US', options.gender || 'neutral');
      if (voice) {
        utterance.voice = voice;
        utterance.lang = voice.lang;
      } else {
        utterance.lang = options.lang || 'en-US';
      }

      // Set speech parameters
      utterance.rate = options.rate || 1.0;
      utterance.pitch = options.pitch || 1.0;
      utterance.volume = options.volume || 1.0;

      // Set event handlers
      utterance.onend = () => {
        console.log('Free TTS: Speech completed');
        this.currentUtterance = null;
        resolve();
      };

      utterance.onerror = (event) => {
        console.error('Free TTS: Speech error:', event.error);
        this.currentUtterance = null;
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      utterance.onstart = () => {
        console.log('Free TTS: Speech started');
      };

      // Start speaking
      console.log(`Free TTS: Speaking text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
      this.synthesis.speak(utterance);
    });
  }

  /**
   * Stop current speech
   */
  stop() {
    if (this.synthesis.speaking) {
      this.synthesis.cancel();
    }
    this.currentUtterance = null;
  }

  /**
   * Pause current speech
   */
  pause() {
    if (this.synthesis.speaking && !this.synthesis.paused) {
      this.synthesis.pause();
    }
  }

  /**
   * Resume paused speech
   */
  resume() {
    if (this.synthesis.paused) {
      this.synthesis.resume();
    }
  }

  /**
   * Check if currently speaking
   * @returns {boolean} - True if speaking
   */
  isSpeaking() {
    return this.synthesis.speaking;
  }

  /**
   * Get list of available voices
   * @returns {Array} - Array of voice objects
   */
  getAvailableVoices() {
    return this.voices.map(voice => ({
      name: voice.name,
      lang: voice.lang,
      gender: this.guessGender(voice.name),
      local: voice.localService,
      default: voice.default
    }));
  }

  /**
   * Guess gender from voice name
   * @param {string} name - Voice name
   * @returns {string} - Guessed gender
   */
  guessGender(name) {
    const nameLower = name.toLowerCase();
    if (nameLower.includes('female') || nameLower.includes('woman') || 
        nameLower.includes('girl') || nameLower.includes('zira') || 
        nameLower.includes('hazel') || nameLower.includes('susan')) {
      return 'female';
    } else if (nameLower.includes('male') || nameLower.includes('man') || 
               nameLower.includes('boy') || nameLower.includes('david') || 
               nameLower.includes('mark') || nameLower.includes('james')) {
      return 'male';
    }
    return 'neutral';
  }

  /**
   * Handle TTS instructions from server
   * @param {object} instructions - TTS instructions from server
   */
  async handleServerInstructions(instructions) {
    if (instructions.type === 'browser-tts') {
      await this.speak(instructions.text, instructions.voice);
    }
  }
}

// Create global instance
window.freeTTS = new FreeTTS();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FreeTTS;
}

// Add CSS for TTS controls (optional)
const style = document.createElement('style');
style.textContent = `
  .tts-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin: 10px 0;
  }
  
  .tts-button {
    padding: 5px 10px;
    border: 1px solid #ccc;
    background: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  }
  
  .tts-button:hover {
    background: #e5e5e5;
  }
  
  .tts-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .tts-status {
    font-size: 12px;
    color: #666;
  }
`;
document.head.appendChild(style);

console.log('Free TTS: Browser TTS module loaded');
console.log('Free TTS: Speech synthesis supported:', window.freeTTS.isSupported);
console.log('Free TTS: Available voices:', window.freeTTS.getAvailableVoices().length);

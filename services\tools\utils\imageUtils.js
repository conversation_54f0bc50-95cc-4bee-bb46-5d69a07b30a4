import fs from 'fs/promises';
import path from 'path';
// We will get createPartFrom<PERSON>ri from the genAI instance passed to prepareImagePart,
// so direct import from @google/genai here is not strictly necessary if genAI is always provided.
// However, keeping it for clarity or potential direct use:
import { createPartFrom<PERSON>ri as genAICreatePartFromUri } from '@google/genai';


const MAX_INLINE_SIZE_BYTES = 20 * 1024 * 1024; // 20MB

const SUPPORTED_MIME_TYPES = {
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.webp': 'image/webp',
  '.heic': 'image/heic',
  '.heif': 'image/heif',
  // Common GIF type
  '.gif': 'image/gif', 
};

/**
 * Determines if a source is a URL.
 * @param {string} source - The source string.
 * @returns {boolean} True if the source is a URL, false otherwise.
 */
function isUrl(source) {
  if (typeof source !== 'string') return false;
  try {
    new URL(source);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * Infers MIME type from file extension.
 * @param {string} filePathOrUrl - The file path or URL.
 * @returns {string|undefined} The inferred MIME type or undefined.
 */
function inferMimeType(filePathOrUrl) {
  const ext = path.extname(filePathOrUrl || '').toLowerCase();
  return SUPPORTED_MIME_TYPES[ext];
}

/**
 * Fetches an image from a URL and returns its ArrayBuffer and inferred MIME type.
 * @param {string} url - The URL of the image.
 * @returns {Promise<{buffer: ArrayBuffer, mimeType: string}>}
 */
async function fetchImageFromUrl(url) {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch image from URL: ${url}, status: ${response.status} ${response.statusText}`);
  }
  const buffer = await response.arrayBuffer();
  const contentType = response.headers.get('content-type');
  const mimeType = contentType?.startsWith('image/') ? contentType.split(';')[0].trim() : inferMimeType(url) || 'application/octet-stream';
  console.log(`Fetched ${url}, content-type: ${contentType}, inferred/used mimeType: ${mimeType}`);
  return { buffer, mimeType };
}

/**
 * Reads an image from a local file path and returns its Buffer and inferred MIME type.
 * @param {string} filePath - The local path to the image file.
 * @returns {Promise<{buffer: Buffer, mimeType: string}>}
 */
async function readImageFromLocalPath(filePath) {
  const buffer = await fs.readFile(filePath);
  const mimeType = inferMimeType(filePath);
  if (!mimeType) {
    throw new Error(`Could not infer MIME type for local file: ${filePath}. Ensure it has a supported extension (${Object.keys(SUPPORTED_MIME_TYPES).join(', ')}).`);
  }
  return { buffer, mimeType };
}

/**
 * Prepares a single image part for the Gemini API request.
 * Handles inline data for small files and uploads to File API for large files.
 * @param {string} source - URL or local file path of the image.
 * @param {import('@google/genai').GoogleGenAIInstance} genAI - The GoogleGenAI instance (from GeminiProvider.genAI).
 * @param {string} [forcedMimeType] - Optionally force a specific MIME type.
 * @returns {Promise<object>} The image part for the Gemini API contents array.
 */
export async function prepareImagePart(source, genAI, forcedMimeType) {
  let imageBuffer;
  let mimeType;
  let sourceIsUrl = isUrl(source);
  let displayName = sourceIsUrl ? path.basename(new URL(source).pathname) : path.basename(source);
  // Sanitize display name
  displayName = displayName.replace(/[^a-zA-Z0-9_.-]/g, '_');


  if (sourceIsUrl) {
    console.log(`imageUtils: Fetching URL: ${source}`);
    const { buffer, mimeType: fetchedMimeType } = await fetchImageFromUrl(source);
    imageBuffer = buffer;
    mimeType = forcedMimeType || fetchedMimeType;
  } else {
    // For now, we are primarily focusing on URL sources for agent integration.
    // Local file path access from a server-side agent needs careful consideration for security.
    // This function can still be used if the agent has a secure way to access local files.
    console.log(`imageUtils: Reading local path: ${source}`);
    const { buffer, mimeType: inferredMimeType } = await readImageFromLocalPath(source);
    imageBuffer = buffer;
    mimeType = forcedMimeType || inferredMimeType;
  }

  if (!mimeType || (!Object.values(SUPPORTED_MIME_TYPES).includes(mimeType) && mimeType === 'application/octet-stream' && !forcedMimeType)) {
    console.warn(`imageUtils: Could not determine a specific supported image MIME type for ${source}. Inferred: ${mimeType}. Attempting to proceed, but Gemini might reject it. Supported: ${Object.keys(SUPPORTED_MIME_TYPES).join(', ')}`);
    if (mimeType === 'application/octet-stream' && !forcedMimeType) {
        // If it's still octet-stream and not forced, it's likely an issue.
         throw new Error(`imageUtils: Ambiguous MIME type 'application/octet-stream' for ${source}. Please ensure the source provides a clear image content-type or the file has a supported extension.`);
    }
  }


  const imageData = Buffer.from(imageBuffer);

  if (imageData.length < MAX_INLINE_SIZE_BYTES) {
    console.log(`imageUtils: Preparing inlineData for ${source} (MIME: ${mimeType}, Size: ${(imageData.length / 1024).toFixed(2)}KB)`);
    return {
      inlineData: {
        mimeType: mimeType,
        data: imageData.toString('base64'),
      },
    };
  } else {
    console.log(`imageUtils: Image size (${(imageData.length / (1024*1024)).toFixed(2)}MB) for ${source} exceeds inline limit (${MAX_INLINE_SIZE_BYTES / (1024*1024)}MB). Uploading via File API...`);
    
    // The genAI instance passed should be the one from GoogleGenAI, which has the 'files' service.
    if (!genAI || !genAI.uploadFile || !genAI.getFile) { // Check for expected File API methods on genAI object
        throw new Error("imageUtils: GoogleGenAI instance with File API capabilities (e.g., genAI.uploadFile) is required for large file uploads.");
    }

    // Note: The SDK's uploadFile expects a path or ReadStream for Node.js, or File/Blob for web.
    // To use the buffer directly, we might need to adapt or use a different approach if available,
    // or temporarily write to a file if running in Node and no direct buffer upload is clean.
    // For now, assuming genAI.uploadFile can handle a Blob-like structure or we adapt.
    // Let's try creating a Blob-like object for Node.js if direct buffer upload isn't straightforward.
    // However, the original code used genAI.files.upload({ file: fileBlob ...})
    // The new SDK (v0.7.0+) uses genAI.uploadFile(path_or_blob, {displayName, mimeType})
    // and genAI.getFile(name)

    let uploadResult;
    try {
        // Create a temporary file path for Node.js environment to use with uploadFile
        // This is a common workaround if direct buffer upload isn't supported or is complex.
        // A more direct buffer upload would be preferable if the SDK allows.
        // For now, let's assume the user's original code with genAI.files.upload was for an older SDK version
        // or a specific setup. The current @google/genai SDK (as of v0.7.0+) uses genAI.uploadFile.
        // The `createPartFromUri` is from `@google/genai` which implies a newer SDK.
        // The `genAI.files.upload` is from an older pattern.
        // Let's stick to the newer SDK pattern if `genAI` is the top-level `GoogleGenerativeAI` instance.
        // The `DescribeImageTool` passes `this.geminiProvider.genAI` which IS the `GoogleGenerativeAI` instance.

        console.log(`imageUtils: Attempting to upload ${displayName} with MIME type ${mimeType} using genAI.uploadFile`);
        uploadResult = await genAI.uploadFile(imageData, { // Pass Buffer directly
            mimeType: mimeType,
            displayName: displayName,
        });
        console.log(`imageUtils: Upload initiated for ${displayName}. File Name: ${uploadResult.file.name}, URI: ${uploadResult.file.uri}`);

        let fileResponse = uploadResult.file; // Initial response from uploadFile

        // Polling for ACTIVE state
        let retries = 0;
        const maxRetries = 12; // Approx 1 minute
        while (fileResponse.state === 'PROCESSING' && retries < maxRetries) {
            retries++;
            console.log(`imageUtils: File ${fileResponse.name} is still processing (Attempt ${retries}/${maxRetries}). State: ${fileResponse.state}. Retrying in 5 seconds...`);
            await new Promise((resolve) => setTimeout(resolve, 5000));
            const getFileResponse = await genAI.getFile(fileResponse.name); // Use getFile with just the name
            fileResponse = getFileResponse.file;
        }

        if (fileResponse.state === 'FAILED') {
            console.error(`imageUtils: File processing failed for ${fileResponse.name}:`, fileResponse);
            throw new Error(`File processing failed for ${fileResponse.name}. Reason: ${fileResponse.error?.message || 'Unknown error'}`);
        }
        if (fileResponse.state !== 'ACTIVE') {
            console.error(`imageUtils: File ${fileResponse.name} is not active after processing. State: ${fileResponse.state}`);
            throw new Error(`File ${fileResponse.name} is not active after processing. State: ${fileResponse.state}`);
        }
        console.log(`imageUtils: File ${fileResponse.name} processed successfully. URI: ${fileResponse.uri}`);
        // Use the genAICreatePartFromUri imported at the top
        return genAICreatePartFromUri(fileResponse.uri, fileResponse.mimeType);

    } catch (uploadError) {
        console.error(`imageUtils: Error during file upload or processing for ${displayName}:`, uploadError);
        throw new Error(`Failed to upload or process large image ${displayName}: ${uploadError.message}`);
    }
  }
}

/**
 * Prepares multiple image parts for the Gemini API request.
 * @param {string[]} sources - Array of URLs or local file paths for the images.
 * @param {import('@google/genai').GoogleGenAIInstance} genAI - The GoogleGenAI instance.
 * @returns {Promise<object[]>} An array of image parts.
 */
export async function prepareMultipleImageParts(sources, genAI) {
  const imageParts = [];
  for (const source of sources) {
    const part = await prepareImagePart(source, genAI); // genAI is passed through
    imageParts.push(part);
  }
  return imageParts;
}

/**
 * Denormalizes bounding box coordinates from the 0-1000 scale to original image pixel values.
 * @param {number[]} normalizedCoords - Array [ymin, xmin, ymax, xmax] with values in 0-1000 range.
 * @param {number} originalImageWidth - The width of the original image in pixels.
 * @param {number} originalImageHeight - The height of the original image in pixels.
 * @returns {number[]} Array [y_px_min, x_px_min, y_px_max, x_px_max] in pixel values.
 * @throws {Error} if inputs are invalid.
 */
export function denormalizeCoordinates(normalizedCoords, originalImageWidth, originalImageHeight) {
  if (!Array.isArray(normalizedCoords) || normalizedCoords.length !== 4) {
    throw new Error('normalizedCoords must be an array of 4 numbers [ymin, xmin, ymax, xmax].');
  }
  if (typeof originalImageWidth !== 'number' || originalImageWidth <= 0 ||
      typeof originalImageHeight !== 'number' || originalImageHeight <= 0) {
    throw new Error('originalImageWidth and originalImageHeight must be positive numbers.');
  }

  const [ymin, xmin, ymax, xmax] = normalizedCoords;

  return [
    Math.round((ymin / 1000) * originalImageHeight),
    Math.round((xmin / 1000) * originalImageWidth),
    Math.round((ymax / 1000) * originalImageHeight),
    Math.round((xmax / 1000) * originalImageWidth),
  ];
}

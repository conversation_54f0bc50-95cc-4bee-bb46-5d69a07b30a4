// services/tools/emailTool.js

import { authorize, listLabels, listMessages } from '../../email.js'; // Adjusted path
import { analyze as analyzeTextNLU } from '../nlu-service.js'; // Import NLU service

class EmailTool {
    constructor() {
        this.name = "gmail";
        this.description = "A tool to interact with Gmail. It can list labels and messages. Requires prior user authorization via Google OAuth.";
        this.usage = "[TOOL: gmail(action, query, maxResults)] where 'action' is 'list_labels' or 'list_messages'. 'query' is optional for 'list_messages' (e.g., 'in:inbox is:unread'). 'maxResults' is optional for 'list_messages' (e.g., 5).";
        this.inputSchema = {
            type: "object",
            properties: {
                action: {
                    type: "string",
                    description: "The action to perform: 'list_labels' or 'list_messages'.",
                    enum: ["list_labels", "list_messages"]
                },
                query: {
                    type: "string",
                    description: "Optional: A Gmail search query string (e.g., 'in:inbox', 'subject:important'). Only used if action is 'list_messages'."
                },
                maxResults: {
                    type: "number",
                    description: "Optional: Maximum number of messages to return. Only used if action is 'list_messages'."
                }
            },
            required: ["action"]
        };
    }

    async execute({ action, query, maxResults }) {
        if (!action) {
            throw new Error("Missing required parameter: action");
        }

        console.log(`📧 [GMAIL TOOL] Attempting to authorize with Google...`);
        let authClient;
        try {
            authClient = await authorize();
            if (!authClient) {
                console.error(`📧 [GMAIL TOOL] Authorization failed. Ensure token.json is present and valid, or re-run authorization flow if it's the first time.`);
                return { error: "Gmail authorization failed. User may need to authorize." };
            }
            console.log(`📧 [GMAIL TOOL] Authorization successful.`);
        } catch (error) {
            console.error(`📧 [GMAIL TOOL] Error during authorization:`, error);
            return { error: `Gmail authorization error: ${error.message}` };
        }

        try {
            if (action === "list_labels") {
                console.log(`📧 [GMAIL TOOL] Listing labels...`);
                // listLabels currently logs to console and returns undefined.
                // For a tool, it should return the data.
                // We'll need to modify listLabels in email.js to return the labels array.
                // For now, we'll adapt by capturing console output or returning a status.
                const labels = await listLabels(authClient);
                console.log(`📧 [GMAIL TOOL] Found ${labels.length} labels.`);
                return labels; // listLabels now returns the array of label objects

            } else if (action === "list_messages") {
                if (query && typeof query !== 'string') {
                    return { error: "Invalid 'query' parameter. It must be a string." };
                }
                console.log(`📧 [GMAIL TOOL] Listing messages with query: "${query || 'all messages'}" and maxResults: ${maxResults}`);
                let messages = await listMessages(authClient, query || '', maxResults);
                console.log(`📧 [GMAIL TOOL] Found ${messages.length} messages. Attempting NLU processing...`);

                const processedMessages = [];
                for (const message of messages) {
                    if (message.body && message.body.trim() !== '') {
                        try {
                            console.log(`📧 [GMAIL TOOL] Processing NLU for message ID: ${message.id}`);
                            const nluResponse = await analyzeTextNLU(message.body, { sourceType: 'email_content' });
                            message.nluAnalysis = nluResponse.nluResult; // Store only the nluResult
                            console.log(`📧 [GMAIL TOOL] NLU processing complete for message ID: ${message.id}`);
                        } catch (nluError) {
                            console.error(`📧 [GMAIL TOOL] NLU processing failed for message ID ${message.id}:`, nluError);
                            message.nluAnalysis = { error: "NLU processing failed", details: nluError.message };
                        }
                    } else {
                        message.nluAnalysis = { note: "Email body was empty or whitespace, NLU not performed." };
                    }
                    processedMessages.push(message);
                }
                return processedMessages;

            } else {
                return { error: `Invalid action: ${action}. Valid actions are 'list_labels' or 'list_messages'.` };
            }
        } catch (error) {
            console.error(`📧 [GMAIL TOOL] Error executing Gmail action '${action}':`, error);
            return { error: `Error performing Gmail action: ${error.message}` };
        }
    }

    getCommandsDocumentation() {
        return [
            `- list_labels: Lists all labels in the user's Gmail account. (Example: TOOL_CALL: gmail: list_labels)`,
            `- list_messages [query] [maxResults]: Lists messages. 'query' is an optional Gmail search string. 'maxResults' is an optional number to limit results. (Example: TOOL_CALL: gmail: list_messages "in:inbox is:unread" 5)`
        ].join('\n');
    }
}

// Export an instance of the EmailTool
export const emailTool = new EmailTool();

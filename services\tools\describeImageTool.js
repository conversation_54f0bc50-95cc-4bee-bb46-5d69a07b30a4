import { GeminiProvider } from '../providers/GeminiProvider.js';
import { prepareImagePart, prepareMultipleImageParts } from './utils/imageUtils.js';
import dotenv from 'dotenv';

dotenv.config(); // To load GEMINI_API_KEY if not passed directly

/**
 * Tool to describe or answer questions about one or more images using Gemini API.
 */
export class DescribeImageTool {
  /**
   * @param {string} [apiKey] - Gemini API key. If not provided, attempts to load from process.env.GOOGLE_API_KEY.
   * @param {string} [modelName] - Gemini model name. Defaults to 'gemini-2.0-flash-lite' (via GeminiProvider).
   */
  constructor(apiKey, modelName) { // modelName will be passed to GeminiProvider
    // Prioritize passed apiKey, then GOOGLE_API_KEY (common in this project), then GEMINI_API_KEY
    const effectiveApiKey = apiKey || process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
    if (!effectiveApiKey) {
        throw new Error("DescribeImageTool: API key not found. Please ensure GOOGLE_API_KEY or GEMINI_API_KEY is set in .env or passed to constructor.");
    }
    this.geminiProvider = new GeminiProvider(effectiveApiKey, modelName); // modelName can be undefined, GeminiProvider will use its default
    this.name = 'describeImage';
    this.description = 'Generates a text description, caption, or answers questions about one or more images. Input should be a JSON string with "sources" (string URL for one image, or array of string URLs for multiple) and "prompt" (string: e.g., "Describe this image.", "What is happening here?", "Compare these images."). For now, only image URLs are reliably supported.';
    // Schema is useful for validation if we implement it, but Agent.js currently relies on getCommandsDocumentation for LLM instruction.
    this.schema = {
      type: 'object',
      properties: {
        sources: {
          oneOf: [
            { type: 'string', description: 'URL of a single image.' },
            { type: 'array', items: { type: 'string' }, description: 'Array of URLs for multiple images.' },
          ],
          description: 'The source(s) of the image(s). Can be a single URL or an array of URLs. Local file paths are not supported in this version.',
        },
        prompt: {
          type: 'string',
          description: 'The text prompt to guide the description or question (e.g., "Describe this image in detail.", "What are the key differences between these two pictures?").',
        },
      },
      required: ['sources', 'prompt'],
    };
  }

  /**
   * Generates documentation for the LLM on how to use this tool.
   * @returns {string}
   */
  getCommandsDocumentation() {
    return `- execute [json_string_arguments]: ${this.description} The argument must be a single, valid JSON string enclosed in single quotes. If an image has been uploaded by the user (and thus available in context), use "imageDataFromContext" as the value for "sources". Example for a single uploaded image: TOOL_CALL: ${this.name}:execute '{"sources": "imageDataFromContext", "prompt": "Describe this image."}' Example for a specific URL (if user provided a URL directly in chat): TOOL_CALL: ${this.name}:execute '{"sources": "https://example.com/image.jpg", "prompt": "What is this?"}' Example for multiple URLs: TOOL_CALL: ${this.name}:execute '{"sources": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"], "prompt": "Compare these."}'`;
  }

  /**
   * Runs the image description tool.
   * @param {string} jsonStringArgs - A JSON string containing 'sources' and 'prompt'.
   * @returns {Promise<string>} A promise that resolves to the generated text.
   */
  async execute(jsonStringArgs) {
    if (!jsonStringArgs || typeof jsonStringArgs !== 'string') {
      console.error(`${this.name}:execute - Error: Arguments must be a JSON string. Received:`, jsonStringArgs);
      throw new Error('Arguments for describeImage tool must be a valid JSON string.');
    }
    let args;
    let cleanedJsonStringArgs = jsonStringArgs; // Define here for broader scope if needed for logging
    try {
      // Remove leading/trailing single quotes if present, then parse
      cleanedJsonStringArgs = jsonStringArgs.replace(/^'|'$/g, '');
      args = JSON.parse(cleanedJsonStringArgs);
    } catch (error) {
      // Only log original jsonStringArgs in catch if cleaned one might not be set due to error before its assignment
      console.error(`${this.name}:execute - Error: Invalid JSON string for arguments. Original:`, jsonStringArgs, `Attempted to clean to:`, cleanedJsonStringArgs, `Parse Error:`, error);
      throw new Error(`Invalid JSON string for describeImage arguments: ${error.message}`);
    }

    const { sources, prompt } = args;

    if (!sources) {
      throw new Error('Image source(s) (args.sources) are required for describeImage tool.');
    }
    if (!prompt) {
      throw new Error('A prompt (args.prompt) is required for describeImage tool.');
    }
    // Basic validation for URL focus
    const checkSources = Array.isArray(sources) ? sources : [sources];
    for (const source of checkSources) {
        if (typeof source !== 'string') {
            console.error(`${this.name}:execute - Error: Image source must be a string. Received:`, typeof source);
            throw new Error(`Invalid image source type: '${typeof source}'. Must be a string (URL or local path).`);
        }
        // Relaxed check: imageUtils will determine if it's a URL or local path.
        // The main concern for the agent is receiving a valid string it can pass on.
        // Specific validation for "is this a functional URL/path" happens in imageUtils.
        // We still log a warning if it's not an HTTP URL, as that's the primary expected path for agent use.
        if (!source.toLowerCase().startsWith('http') && !source.startsWith('data:')) {
            console.warn(`${this.name}:execute - Warning: Image source '${source}' is not an HTTP(S) URL or Data URL. Assuming it's a local path for direct tool testing or specific server-side access.`);
        }
    }


    try {
      let imageParts = [];
      if (Array.isArray(sources)) {
        console.log(`DescribeImageTool: Preparing multiple image parts from sources: ${sources.join(', ')}`);
        // Pass the genAI instance from the provider
        imageParts = await prepareMultipleImageParts(sources, this.geminiProvider.genAI);
      } else if (typeof sources === 'string') {
        console.log(`DescribeImageTool: Preparing single image part from source: ${sources}`);
        // Pass the genAI instance from the provider
        const part = await prepareImagePart(sources, this.geminiProvider.genAI);
        imageParts.push(part);
      } else {
        throw new Error('Invalid "sources" format. Must be a string URL or an array of string URLs.');
      }
      console.log('DescribeImageTool: Image part(s) prepared successfully.');

      // Gemini API expects contents as an array of parts.
      // The prompt text should also be a part.
      // For multimodal, typically text parts come before or interspersed with image parts.
      const contents = [{ text: prompt }, ...imageParts];
      
      const geminiPromptPayload = {
        contents: contents,
        // Optional: Add specific generation config for this tool if needed
        // temperature: 0.5, 
        // maxOutputTokens: 512 
      };

      console.log('DescribeImageTool: Sending request to Gemini API...');
      // Use the generateContent method from our GeminiProvider
      const description = await this.geminiProvider.generateContent(geminiPromptPayload);
      console.log('DescribeImageTool: Description received from Gemini.');
      return description;
    } catch (error) {
      console.error('Error in DescribeImageTool execute method:', error);
      // Return a user-friendly error message, but also keep technical details for logs
      return `Error: I encountered an issue while trying to describe the image(s). Details: ${error.message}`;
    }
  }
}

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url'; // Added import
import { addEntry, ensureBaseDirectories } from './KnowledgeBaseManager.js';

// ES module equivalent of __dirname for resolving relative paths
const __filename = fileURLToPath(import.meta.url); // Corrected line
const __dirname = path.dirname(__filename);

const filesToAdd = [
    { filePath: 'parallel_example_config.js', type: 'configuration', name: 'parallel_example_config.js' },
    { filePath: 'AgentFactory.js', type: 'snippet', name: 'AgentFactory.js' },
    { filePath: 'TeamFactory.js', type: 'snippet', name: 'TeamFactory.js' },
    { filePath: 'AgencyFactory.js', type: 'snippet', name: 'AgencyFactory.js' },
    { filePath: 'Agent.js', type: 'snippet', name: 'Agent.js' },
    { filePath: 'Agency.js', type: 'snippet', name: 'Agency.js' },
    { filePath: 'Team.js', type: 'snippet', name: 'Team.js' },
    { filePath: 'run_parallel_example.js', type: 'snippet', name: 'run_parallel_example.js' },
];

async function populateKnowledgeBase() {
    try {
        console.log('Ensuring knowledge base directories exist...');
        await ensureBaseDirectories();
        console.log('Knowledge base directories ensured.');

        console.log('\nPopulating knowledge base with specified files...');
        for (const fileInfo of filesToAdd) {
            const absoluteFilePath = path.resolve(__dirname, fileInfo.filePath);
            console.log(`\nProcessing file: ${absoluteFilePath}`);
            try {
                const content = await fs.readFile(absoluteFilePath, 'utf8');
                const metadata = {
                    original_path: fileInfo.filePath,
                    source: 'project_file_ingestion'
                };
                
                // Use the specified name for the entry, or derive from filePath if name isn't given
                const entryName = fileInfo.name || path.basename(fileInfo.filePath);

                await addEntry(fileInfo.type, entryName, content, metadata);
                console.log(`Successfully added ${entryName} (type: ${fileInfo.type}) to knowledge base.`);
            } catch (error) {
                console.error(`Failed to read or add file ${fileInfo.filePath}:`, error.message);
                if (error.code === 'ENOENT') {
                    console.error(`Please ensure the file exists at: ${absoluteFilePath}`);
                }
            }
        }
        console.log('\nKnowledge base population process completed.');

    } catch (error) {
        console.error('Error during knowledge base population:', error);
    }
}

// Run the population script
populateKnowledgeBase();

/**
 * NLU Engine Routes
 * 
 * Provides endpoints for natural language understanding capabilities
 */

import express from 'express';
import { analyze } from '../services/nlu-service.js';

const router = express.Router();

/**
 * Analyze a message using the NLU engine
 * POST /api/nlu/analyze
 */
router.post('/analyze', async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ 
        success: false, 
        error: 'Message is required' 
      });
    }
    
    const analysis = await analyze(message);
    
    if (!analysis) {
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to analyze message' 
      });
    }
    
    return res.json({
      success: true,
      analysis
    });
  } catch (error) {
    console.error('Error in NLU analysis:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

export default router;

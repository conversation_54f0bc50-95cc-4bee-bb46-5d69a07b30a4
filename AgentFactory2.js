const { Agent } = require('./Agent.js');
const { GeminiProvider } = require('./providers/GeminiProvider.js');
const { OllamaProvider } = require('./providers/OllamaProvider.js');
const { EventEmitter } = require('./utils/EventEmitter.js');

// Load environment variables (optional but good practice)
require('dotenv').config();

/**
 * Factory class for creating agents with multiple LLM providers
 */
class AgentFactory {
  constructor(config = {}) {
    this.defaultProvider = config.defaultProvider || 'ollama';
    this.apiKeys = config.apiKeys || {};
    this.ollamaConfig = config.ollamaConfig || {
      baseUrl: 'http://localhost:11434',
      model: 'gemma3:1b'
    };
    this.tools = {};
    this.events = new EventEmitter();

    // Register tools provided in the config
    if (config.tools) {
      for (const toolName in config.tools) {
        if (Object.prototype.hasOwnProperty.call(config.tools, toolName)) {
          this.registerTool(toolName, config.tools[toolName]);
        }
      }
    }
  }

  /**
   * Create multiple agents from a configuration object
   * @param {Object} agentsConfig - Object where keys are agent IDs and values are agent configurations
   * @returns {Object} - Object with created agent instances, keyed by their IDs
   */
  createAgents(agentsConfig) {
    const agents = {};

    for (const [agentId, agentConfig] of Object.entries(agentsConfig)) {
      try {
        const configWithId = { ...agentConfig, id: agentConfig.id || agentId };
        agents[agentId] = this.createAgent(configWithId);
        this.events.emit('agentCreated', { agentId, agent: agents[agentId] });
      } catch (error) {
        console.error(`Error creating agent ${agentId}:`, error);
        this.events.emit('agentCreationFailed', { agentId, config: agentConfig, error });
      }
    }

    return agents;
  }

  /**
   * Register a tool that can be used by agents created by this factory
   * @param {string} toolName - Name of the tool
   * @param {Object} toolDefinition - Tool definition object
   */
  registerTool(toolName, toolDefinition) {
    if (!toolDefinition || typeof toolDefinition !== 'object') {
      throw new Error(`Tool definition for "${toolName}" must be an object.`);
    }
    if (typeof toolDefinition.execute !== 'function') {
      throw new Error(`Tool definition for "${toolName}" must include an 'execute' function.`);
    }
    if (typeof toolDefinition.description !== 'string' || !toolDefinition.description) {
      throw new Error(`Tool definition for "${toolName}" must include a non-empty 'description' string.`);
    }
    if (typeof toolDefinition.inputSchema !== 'object' || toolDefinition.inputSchema === null) {
      throw new Error(`Tool definition for "${toolName}" must include an 'inputSchema' object.`);
    }

    this.tools[toolName] = toolDefinition;
    console.log(`Tool registered with factory: ${toolName}`);
    this.events.emit('toolRegistered', { toolName, toolDefinition });
  }

  /**
   * Create an agent from a configuration object
   * @param {Object} agentConfig - Agent configuration
   * @returns {Agent} - Created agent instance
   */
  createAgent(agentConfig) {
    const agentId = agentConfig.id || `agent-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const configWithId = { ...agentConfig, id: agentId };
    const providerName = (agentConfig.provider || this.defaultProvider).toLowerCase();

    console.log(`Creating agent: ${agentId} with provider: ${providerName}`);

    // Create LLM Provider based on type
    let llmProvider;
    
    switch (providerName) {
      case 'ollama':
        llmProvider = this._createOllamaProvider(agentConfig, agentId);
        break;
      case 'gemini':
        llmProvider = this._createGeminiProvider(agentConfig, agentId);
        break;
      default:
        throw new Error(`Unsupported provider: ${providerName}`);
    }

    // Resolve tools for the agent
    const agentTools = this._resolveAgentTools(agentConfig, agentId);

    // Build final agent configuration
    const finalAgentConfig = {
      ...configWithId,
      llmProvider,
      llmConfig: agentConfig.llmConfig || {},
      tools: agentTools
    };

    // Clean up provider name from final config
    delete finalAgentConfig.provider;

    return new Agent(finalAgentConfig);
  }

  /**
   * Create Ollama provider instance
   * @private
   */
  _createOllamaProvider(agentConfig, agentId) {
    // Get Ollama configuration from agent config or factory defaults
    const ollamaConfig = agentConfig.ollamaConfig || this.ollamaConfig;
    const modelName = agentConfig.llmConfig?.model || ollamaConfig.model || 'gemma:1b';
    const baseUrl = ollamaConfig.baseUrl || 'http://localhost:11434';
    
    // Default options for Ollama
    const defaultOptions = {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      num_predict: 2048,
      ...agentConfig.llmConfig?.options
    };

    console.log(`Agent ${agentId}: Using Ollama model "${modelName}" at ${baseUrl}`);
    
    return new OllamaProvider(modelName, baseUrl, defaultOptions);
  }

  /**
   * Create Gemini provider instance
   * @private
   */
  _createGeminiProvider(agentConfig, agentId) {
    // API Key resolution for Gemini
    let apiKey = this.apiKeys.gemini;
    if (!apiKey && process.env.GEMINI_API_KEY) {
      apiKey = process.env.GEMINI_API_KEY;
      console.log(`Agent ${agentId}: Using GEMINI_API_KEY from environment variables.`);
    }

    if (!apiKey) {
      throw new Error(`No API key found for Gemini provider for agent '${agentId}'. Set it in factory config apiKeys or as GEMINI_API_KEY environment variable.`);
    }

    const modelName = agentConfig.llmConfig?.model || 'gemini-2.0-flash-lite';
    return new GeminiProvider(apiKey, modelName);
  }

  /**
   * Resolve tools for an agent
   * @private
   */
  _resolveAgentTools(agentConfig, agentId) {
    const agentTools = {};

    if (Array.isArray(agentConfig.tools)) {
      // Process array of tool names
      for (const toolName of agentConfig.tools) {
        if (typeof toolName === 'string') {
          if (!this.tools[toolName]) {
            console.warn(`Warning: Tool "${toolName}" specified for agent ${agentId} but not registered in the factory.`);
            continue;
          }
          agentTools[toolName] = this.tools[toolName];
        } else {
          console.warn(`Warning: Invalid tool name reference in agent ${agentId} config's tools array:`, toolName);
        }
      }
    } else if (agentConfig.tools && typeof agentConfig.tools === 'object') {
      // Allow providing tool definitions directly in agent config
      console.warn(`Warning: Providing tool definitions directly in agent config for ${agentId}. Prefer registering tools with the factory and referencing by name.`);
      Object.assign(agentTools, agentConfig.tools);
    }

    return agentTools;
  }

  /**
   * Set default Ollama configuration
   * @param {Object} config - Ollama configuration
   * @param {string} config.baseUrl - Base URL for Ollama server
   * @param {string} config.model - Default model name
   */
  setOllamaConfig(config) {
    this.ollamaConfig = { ...this.ollamaConfig, ...config };
    console.log('Updated Ollama configuration:', this.ollamaConfig);
  }

  /**
   * Get list of available tools
   * @returns {Array<string>} - Array of registered tool names
   */
  getAvailableTools() {
    return Object.keys(this.tools);
  }

  /**
   * Get tool definition by name
   * @param {string} toolName - Name of the tool
   * @returns {Object|null} - Tool definition or null if not found
   */
  getTool(toolName) {
    return this.tools[toolName] || null;
  }

  /**
   * Remove a registered tool
   * @param {string} toolName - Name of the tool to remove
   * @returns {boolean} - True if tool was removed, false if not found
   */
  unregisterTool(toolName) {
    if (this.tools[toolName]) {
      delete this.tools[toolName];
      this.events.emit('toolUnregistered', { toolName });
      console.log(`Tool unregistered: ${toolName}`);
      return true;
    }
    return false;
  }

  /**
   * Subscribe to factory events
   * @param {string} eventName - Event name
   * @param {Function} listener - Callback function
   * @returns {Function} - Unsubscribe function
   */
  on(eventName, listener) {
    return this.events.on(eventName, listener);
  }

  /**
   * Unsubscribe from factory events
   * @param {string} eventName - Event name
   * @param {Function} listenerToRemove - Listener function to remove
   */
  off(eventName, listenerToRemove) {
    this.events.off(eventName, listenerToRemove);
  }

  /**
   * Get factory configuration summary
   * @returns {Object} - Current factory configuration
   */
  getConfig() {
    return {
      defaultProvider: this.defaultProvider,
      ollamaConfig: this.ollamaConfig,
      availableTools: this.getAvailableTools(),
      hasGeminiKey: !!this.apiKeys.gemini || !!process.env.GEMINI_API_KEY
    };
  }
}

module.exports = { AgentFactory };
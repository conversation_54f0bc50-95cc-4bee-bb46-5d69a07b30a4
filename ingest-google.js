// ingest-google.js
import dotenv from 'dotenv';
dotenv.config();

import { Pinecone } from '@pinecone-database/pinecone';
import {GoogleGenAI} from '@google/genai';
import fs from 'fs/promises';
import path from 'path';

// --- Configuration ---
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY || 'YOUR_GOOGLE_API_KEY'; // Ensure this is set in your .env or environment
const PINECONE_API_KEY = process.env.PINECONE_API_KEY || 'YOUR_PINECONE_API_KEY';
const PINECONE_ENVIRONMENT = process.env.PINECONE_ENVIRONMENT || 'YOUR_PINECONE_ENVIRONMENT'; // This is not used by Pinecone client v6 constructor but might be used elsewhere or by older scripts.
const PINECONE_INDEX_NAME = 'kb'; // Changed to match your existing serverless index

const CODE_DIRECTORY = './knowledge_base'; // Directory containing your .js files

const CHUNK_SIZE = 1000; // Character count for chunks (adjust as needed)
const CHUNK_OVERLAP = 100; // Character count for overlap (adjust as needed)
const SNIPPET_LENGTH = 200; // Length for the 'snippet' metadata field

// --- Initialize Clients ---
if (!GOOGLE_API_KEY || GOOGLE_API_KEY === 'YOUR_GOOGLE_API_KEY') {
  console.error("Error: GOOGLE_API_KEY is not set. Please set it in your environment variables or .env file.");
  process.exit(1);
}
if (!PINECONE_API_KEY || PINECONE_API_KEY === 'YOUR_PINECONE_API_KEY') {
    console.error("Error: PINECONE_API_KEY is not set. Please set it in your environment variables or .env file.");
    process.exit(1);
}
if (!PINECONE_ENVIRONMENT || PINECONE_ENVIRONMENT === 'YOUR_PINECONE_ENVIRONMENT') {
    console.error("Error: PINECONE_ENVIRONMENT is not set. Please set it in your environment variables or .env file.");
  process.exit(1);
}

const genAI = new GoogleGenAI({ apiKey: GOOGLE_API_KEY });

const pc = new Pinecone({
  apiKey: PINECONE_API_KEY,
});
let index; // Will be initialized after check/create

const EMBEDDING_DIMENSION = 768; // Same as in KnowledgeBaseManager.js

// --- Helper Functions ---

// Simple text splitter
function simpleSplitter(text, chunkSize, chunkOverlap) {
  const chunks = [];
  let i = 0;
  while (i < text.length) {
    const end = Math.min(i + chunkSize, text.length);
    chunks.push(text.substring(i, end));
    i += chunkSize - chunkOverlap;
    if (i >= text.length && end < text.length) { // Ensure last part is captured if overlap pushes 'i' beyond length
        chunks.push(text.substring(end - chunkOverlap));
    } else if (i < chunkOverlap && chunks.length > 1) { // Avoid negative start index if overlap > remaining text
        i = end; // Move to the end of the last chunk if overlap is too large
    }
  }
  // A simple fix for potential empty last chunk if text length is a multiple of (chunkSize - chunkOverlap)
  if (chunks.length > 0 && chunks[chunks.length -1] === "") {
      chunks.pop();
  }
  return chunks.filter(chunk => chunk.trim() !== ''); // Remove empty or whitespace-only chunks
}


async function getFilesInDirectory(dir) {
  let files = [];
  const items = await fs.readdir(dir, { withFileTypes: true });
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory()) {
      files = files.concat(await getFilesInDirectory(fullPath));
    } else if (item.isFile() && item.name.endsWith('.js')) {
      files.push(fullPath);
    }
  }
  return files;
}

async function processFile(filePath) {
  const content = await fs.readFile(filePath, 'utf-8');
  const relativePath = path.relative(process.cwd(), filePath); // Relative to project root
  const fileName = path.basename(filePath);
  const entryName = fileName.replace(/\.js$/, '');

  console.log(`Processing file: ${relativePath}`);

  const chunks = simpleSplitter(content, CHUNK_SIZE, CHUNK_OVERLAP);

  if (chunks.length === 0) {
    console.log(`No content chunks generated for ${relativePath}. Skipping.`);
    return [];
  }

  const vectorsToUpsert = [];
  for (let i = 0; i < chunks.length; i++) {
    const chunkText = chunks[i];

    const snippet = chunkText.substring(0, SNIPPET_LENGTH) + (chunkText.length > SNIPPET_LENGTH ? '...' : '');

    try {
      const result = await genAI.models.embedContent({
        model: "gemini-embedding-004", // Using the model from embeddingService.js
        contents: [chunkText]
      });
      const embedding = result.embeddings[0].values; // Accessing the embedding

      vectorsToUpsert.push({
        id: `${relativePath}-${i}`, // Keep ID structure for now, can be re-evaluated
        values: embedding,
        metadata: {
          type: 'snippet', // Changed from entry_type
          name: entryName,   // Changed from entry_name
          fullContent: chunkText, // Added, using chunkText as the full content for this vector
          relativePath: relativePath,
          // text_chunk: chunkText, // Replaced by fullContent for consistency
          previewSnippet: snippet, // Renamed 'snippet' to 'previewSnippet' to avoid confusion if 'snippet' means something else
          chunk_index: i,
          file_name: fileName,
          source_script: 'ingest-google.js' // Added to identify origin
        },
      });
    } catch (embeddingError) {
      console.error(`Error embedding chunk ${i} from ${relativePath}:`, embeddingError.message);
      // Optionally, decide if you want to skip this chunk or stop the process
    }
  }
  return vectorsToUpsert;
}

async function ingestKnowledgeBase() {
  try {
    // Check if index exists, create if not
    console.log(`Checking for Pinecone index: "${PINECONE_INDEX_NAME}"...`);
    const indexList = await pc.listIndexes();
    if (!indexList.indexes || !indexList.indexes.some(i => i.name === PINECONE_INDEX_NAME)) {
      console.log(`Pinecone index "${PINECONE_INDEX_NAME}" not found. Creating new index...`);
      await pc.createIndex({
        name: PINECONE_INDEX_NAME,
        dimension: EMBEDDING_DIMENSION,
        metric: 'cosine',
        spec: { 
          serverless: {
            cloud: 'aws',
            region: 'us-east-1'
          }
        }
      });
      console.log(`Pinecone index "${PINECONE_INDEX_NAME}" created successfully with serverless spec. It may take a few moments to initialize.`);
      await new Promise(resolve => setTimeout(resolve, 15000)); // Increased wait time for new index
    } else {
      console.log(`Pinecone index "${PINECONE_INDEX_NAME}" found.`);
      // If index already exists, then attempt to delete all vectors
      console.log(`Attempting to delete all vectors from existing index: "${PINECONE_INDEX_NAME}"...`);
      try {
        index = pc.Index(PINECONE_INDEX_NAME); // Initialize index object before deleteAll
        await index.deleteAll();
        console.log(`All vectors deleted from index "${PINECONE_INDEX_NAME}". Waiting a moment for operation to complete...`);
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      } catch (deleteError) {
        console.error(`Error deleting vectors from index "${PINECONE_INDEX_NAME}":`, deleteError);
        console.warn("Proceeding with ingestion, but old data might persist or conflict if deletion failed.");
      }
    }
    
    // Ensure index object is initialized
    if (!index) {
        index = pc.Index(PINECONE_INDEX_NAME);
    }
    console.log(`Connected to Pinecone index "${PINECONE_INDEX_NAME}" for ingestion.`);

    console.log(`Looking for .js files in: ${path.resolve(CODE_DIRECTORY)}`);
    const jsFiles = await getFilesInDirectory(CODE_DIRECTORY);

    if (jsFiles.length === 0) {
      console.log(`No .js files found in ${CODE_DIRECTORY}. Exiting.`);
      return;
    }
    console.log(`Found ${jsFiles.length} .js files to process.`);

    let allVectors = [];
    for (const filePath of jsFiles) {
      const fileVectors = await processFile(filePath);
      allVectors = allVectors.concat(fileVectors);
    }

    if (allVectors.length === 0) {
      console.log("No vectors generated. Ingestion complete (nothing to upsert).");
      return;
    }

    console.log(`Prepared ${allVectors.length} vectors for upserting.`);

    const BATCH_SIZE = 100; // Pinecone upsert limit
    for (let i = 0; i < allVectors.length; i += BATCH_SIZE) {
      const batch = allVectors.slice(i, i + BATCH_SIZE);
      console.log(`Upserting batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(allVectors.length / BATCH_SIZE)} (size: ${batch.length})`);
      await index.upsert(batch);
    }

    console.log('Knowledge base ingestion complete!');

  } catch (error) {
    console.error('Error during ingestion:', error);
    if (error.message && error.message.includes('PineconeClient: Error fetching environment')) {
        console.error("This might be due to an incorrect PINECONE_ENVIRONMENT or PINECONE_API_KEY.");
    }
    if (error.message && error.message.includes('found no records to upsert')) {
        console.error("This specific error might mean the Pinecone index is not ready or there's an issue with the vector format/IDs.");
    }
  }
}

// --- Run the ingestion process ---
(async () => {
  // Ensure environment variables are loaded if using a .env file
  // If you're not using dotenv, you can remove this.
  // import dotenv from 'dotenv'; // This was moved to the top
  // dotenv.config(); // This was moved to the top

  await ingestKnowledgeBase();
})();

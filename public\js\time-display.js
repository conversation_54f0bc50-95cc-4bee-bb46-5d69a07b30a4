/**
 * Time Display Manager
 * 
 * Handles the display of timestamps in the chat UI, including:
 * - Toggling timestamp visibility
 * - Switching between absolute and relative time formats
 * - Grouping messages by day and time period
 */

class TimeDisplayManager {
  constructor() {
    // Default settings
    this.settings = {
      showTimestamps: true,
      timeFormat: 'relative', // 'absolute' or 'relative'
      groupByDay: true,
      groupByTimePeriod: false
    };
    
    // Load settings from localStorage
    this.loadSettings();
    
    // Initialize UI
    this.initUI();
    
    // Apply current settings
    this.applySettings();
  }
  
  /**
   * Load settings from localStorage
   */
  loadSettings() {
    const savedSettings = localStorage.getItem('timeDisplaySettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        this.settings = { ...this.settings, ...parsed };
      } catch (e) {
        console.error('Error loading time display settings:', e);
      }
    }
  }
  
  /**
   * Save settings to localStorage
   */
  saveSettings() {
    localStorage.setItem('timeDisplaySettings', JSON.stringify(this.settings));
  }
  
  /**
   * Initialize the UI elements
   */
  initUI() {
    // Create the toggle button
    this.createToggleButton();
    
    // Create the settings panel
    this.createSettingsPanel();
    
    // Add event listeners
    this.addEventListeners();
  }
  
  /**
   * Create the toggle button in the UI
   */
  createToggleButton() {
    // Find the chat header or create a container for the toggle
    const chatHeader = document.querySelector('.chat-header');
    if (!chatHeader) return;
    
    // Create the toggle button
    const toggleButton = document.createElement('div');
    toggleButton.className = 'time-display-toggle';
    toggleButton.innerHTML = '<i class="fas fa-clock"></i>';
    toggleButton.title = 'Time Display Settings';
    
    // Add to the UI
    chatHeader.appendChild(toggleButton);
    
    // Store reference
    this.toggleButton = toggleButton;
  }
  
  /**
   * Create the settings panel
   */
  createSettingsPanel() {
    // Create the settings panel
    const settingsPanel = document.createElement('div');
    settingsPanel.className = 'time-display-settings';
    settingsPanel.innerHTML = `
      <h3>Time Display Settings</h3>
      
      <div class="time-display-option">
        <label>
          <input type="checkbox" name="showTimestamps" ${this.settings.showTimestamps ? 'checked' : ''}>
          Show timestamps
        </label>
      </div>
      
      <div class="time-display-option">
        <label>
          <input type="radio" name="timeFormat" value="relative" ${this.settings.timeFormat === 'relative' ? 'checked' : ''}>
          Relative time (e.g., "5 minutes ago")
        </label>
        <div class="description">Shows how long ago messages were sent</div>
      </div>
      
      <div class="time-display-option">
        <label>
          <input type="radio" name="timeFormat" value="absolute" ${this.settings.timeFormat === 'absolute' ? 'checked' : ''}>
          Absolute time (e.g., "3:45 PM")
        </label>
        <div class="description">Shows the exact time messages were sent</div>
      </div>
      
      <div class="time-display-option">
        <label>
          <input type="checkbox" name="groupByDay" ${this.settings.groupByDay ? 'checked' : ''}>
          Group by day
        </label>
        <div class="description">Shows headers for Today, Yesterday, etc.</div>
      </div>
      
      <div class="time-display-option">
        <label>
          <input type="checkbox" name="groupByTimePeriod" ${this.settings.groupByTimePeriod ? 'checked' : ''}>
          Group by time period
        </label>
        <div class="description">Shows headers for Morning, Afternoon, etc.</div>
      </div>
    `;
    
    // Add to the UI
    document.body.appendChild(settingsPanel);
    
    // Store reference
    this.settingsPanel = settingsPanel;
  }
  
  /**
   * Add event listeners
   */
  addEventListeners() {
    // Toggle button click
    this.toggleButton.addEventListener('click', () => {
      this.settingsPanel.classList.toggle('active');
    });
    
    // Close panel when clicking outside
    document.addEventListener('click', (e) => {
      if (!this.settingsPanel.contains(e.target) && !this.toggleButton.contains(e.target)) {
        this.settingsPanel.classList.remove('active');
      }
    });
    
    // Settings changes
    this.settingsPanel.querySelectorAll('input').forEach(input => {
      input.addEventListener('change', () => {
        if (input.type === 'checkbox') {
          this.settings[input.name] = input.checked;
        } else if (input.type === 'radio' && input.checked) {
          this.settings[input.name] = input.value;
        }
        
        this.saveSettings();
        this.applySettings();
      });
    });
  }
  
  /**
   * Apply the current settings to the UI
   */
  applySettings() {
    // Get the chat container
    const chatContainer = document.querySelector('.chat-messages');
    if (!chatContainer) return;
    
    // Toggle timestamp visibility
    if (this.settings.showTimestamps) {
      chatContainer.classList.remove('time-hidden');
    } else {
      chatContainer.classList.add('time-hidden');
    }
    
    // Apply time format
    this.updateTimeFormat();
    
    // Apply grouping
    this.updateMessageGrouping();
  }
  
  /**
   * Update the time format (absolute vs relative)
   */
  updateTimeFormat() {
    const timeElements = document.querySelectorAll('.message-time');
    
    timeElements.forEach(el => {
      const timestamp = el.getAttribute('data-timestamp');
      if (!timestamp) return;
      
      if (this.settings.timeFormat === 'relative') {
        el.textContent = this.formatRelativeTime(new Date(timestamp));
        el.classList.remove('absolute');
        el.classList.add('relative');
      } else {
        el.textContent = this.formatAbsoluteTime(new Date(timestamp));
        el.classList.remove('relative');
        el.classList.add('absolute');
      }
    });
  }
  
  /**
   * Update message grouping based on settings
   */
  updateMessageGrouping() {
    // Get all messages
    const chatContainer = document.querySelector('.chat-messages');
    if (!chatContainer) return;
    
    // Remove existing headers
    chatContainer.querySelectorAll('.time-header').forEach(el => el.remove());
    
    // If grouping is disabled, we're done
    if (!this.settings.groupByDay && !this.settings.groupByTimePeriod) {
      return;
    }
    
    // Get all message elements
    const messageElements = Array.from(chatContainer.querySelectorAll('.message-bubble'));
    
    // Group by day if enabled
    if (this.settings.groupByDay) {
      this.groupMessagesByDay(messageElements, chatContainer);
    }
    
    // Group by time period if enabled
    if (this.settings.groupByTimePeriod) {
      this.groupMessagesByTimePeriod(messageElements, chatContainer);
    }
  }
  
  /**
   * Group messages by day
   * @param {Array} messageElements - Array of message DOM elements
   * @param {Element} container - Container element
   */
  groupMessagesByDay(messageElements, container) {
    let currentDay = null;
    
    messageElements.forEach(el => {
      const timestamp = el.querySelector('.message-time')?.getAttribute('data-timestamp');
      if (!timestamp) return;
      
      const date = new Date(timestamp);
      const day = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
      
      if (day !== currentDay) {
        currentDay = day;
        
        // Create day header
        const header = document.createElement('div');
        header.className = 'time-header day-header time-fade-in';
        header.innerHTML = `<span class="time-header-text">${this.formatDayHeader(date)}</span>`;
        
        // Insert before the message
        el.parentNode.insertBefore(header, el);
      }
    });
  }
  
  /**
   * Group messages by time period
   * @param {Array} messageElements - Array of message DOM elements
   * @param {Element} container - Container element
   */
  groupMessagesByTimePeriod(messageElements, container) {
    let currentPeriod = null;
    let currentDay = null;
    
    messageElements.forEach(el => {
      const timestamp = el.querySelector('.message-time')?.getAttribute('data-timestamp');
      if (!timestamp) return;
      
      const date = new Date(timestamp);
      const day = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
      const period = this.getTimePeriod(date);
      
      // Reset period when day changes
      if (day !== currentDay) {
        currentDay = day;
        currentPeriod = null;
      }
      
      if (period !== currentPeriod) {
        currentPeriod = period;
        
        // Create period header
        const header = document.createElement('div');
        header.className = 'time-header period-header time-fade-in';
        header.innerHTML = `<span class="time-header-text">${period.charAt(0).toUpperCase() + period.slice(1)}</span>`;
        
        // Insert before the message
        el.parentNode.insertBefore(header, el);
      }
    });
  }
  
  /**
   * Format a timestamp as an absolute time
   * @param {Date} date - The date to format
   * @returns {string} - Formatted time string
   */
  formatAbsoluteTime(date) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
  
  /**
   * Format a timestamp as a relative time
   * @param {Date} date - The date to format
   * @returns {string} - Relative time string
   */
  formatRelativeTime(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffMin < 1) return 'just now';
    if (diffHour < 1) return `${diffMin}m ago`;
    if (diffDay < 1) return `${diffHour}h ago`;
    if (diffDay === 1) return 'yesterday';
    if (diffDay < 7) return `${diffDay}d ago`;
    
    return date.toLocaleDateString();
  }
  
  /**
   * Format a date as a day header
   * @param {Date} date - The date to format
   * @returns {string} - Formatted day header
   */
  formatDayHeader(date) {
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (this.isSameDay(date, now)) return 'Today';
    if (this.isSameDay(date, yesterday)) return 'Yesterday';
    
    const options = { weekday: 'long', month: 'short', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  }
  
  /**
   * Check if two dates are on the same day
   * @param {Date} date1 - First date
   * @param {Date} date2 - Second date
   * @returns {boolean} - Whether the dates are on the same day
   */
  isSameDay(date1, date2) {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }
  
  /**
   * Get the time period for a date
   * @param {Date} date - The date to categorize
   * @returns {string} - Time period (morning, afternoon, evening, night)
   */
  getTimePeriod(date) {
    const hour = date.getHours();
    
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 21) return 'evening';
    return 'night';
  }
  
  /**
   * Add a timestamp to a message element
   * @param {Element} messageEl - The message element
   * @param {Date} timestamp - The timestamp
   */
  addTimestampToMessage(messageEl, timestamp) {
    // Create timestamp element if it doesn't exist
    let timeEl = messageEl.querySelector('.message-time');
    if (!timeEl) {
      timeEl = document.createElement('span');
      timeEl.className = 'message-time';
      
      // Find the right place to insert the timestamp
      const contentEl = messageEl.querySelector('.message-content');
      if (contentEl) {
        contentEl.appendChild(timeEl);
      } else {
        messageEl.appendChild(timeEl);
      }
    }
    
    // Set the timestamp data attribute
    timeEl.setAttribute('data-timestamp', timestamp.toISOString());
    
    // Format the timestamp according to current settings
    if (this.settings.timeFormat === 'relative') {
      timeEl.textContent = this.formatRelativeTime(timestamp);
      timeEl.classList.add('relative');
    } else {
      timeEl.textContent = this.formatAbsoluteTime(timestamp);
      timeEl.classList.add('absolute');
    }
  }
  
  /**
   * Update timestamps periodically to keep relative times accurate
   */
  startPeriodicUpdates() {
    // Update relative timestamps every minute
    setInterval(() => {
      if (this.settings.timeFormat === 'relative' && this.settings.showTimestamps) {
        this.updateTimeFormat();
      }
    }, 60000); // Every minute
  }
}

// Initialize the time display manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.timeDisplayManager = new TimeDisplayManager();
  window.timeDisplayManager.startPeriodicUpdates();
});

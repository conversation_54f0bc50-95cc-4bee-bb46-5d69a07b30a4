/**
 * Transcript Model
 * 
 * MongoDB schema for storing transcripts and TTS logs.
 */

import mongoose from 'mongoose';

const transcriptSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true
  },
  audioUrl: {
    type: String,
    required: false
  },
  audioLength: {
    type: Number,
    default: 0
  },
  confidence: {
    type: Number,
    default: 0
  },
  voice: {
    type: String,
    default: 'default'
  },
  type: {
    type: String,
    enum: ['transcription', 'tts'],
    required: true
  },
  fallbackMode: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const Transcript = mongoose.model('Transcript', transcriptSchema);

export default Transcript;


import fs from 'fs/promises';
import path from 'path';
import process from 'process';
import { authenticate } from '@google-cloud/local-auth';
import { google } from 'googleapis';

// If modifying these scopes, delete token.json.
// Choose the scope(s) that fit your application's needs.
const SCOPES = ['https://www.googleapis.com/auth/gmail.readonly']; // Read-only access to Gmail
// The file token.json stores the user's access and refresh tokens, and is
// created automatically when the authorization flow completes for the first
// time.
const TOKEN_PATH = path.join(process.cwd(), 'token.json');
const CREDENTIALS_PATH = path.join(process.cwd(), 'credentials2.json'); // Path to your downloaded credentials.json

/**
 * Reads previously authorized credentials from the save file.
 * @return {Promise<OAuth2Client|null>}
 */
export async function loadSavedCredentialsIfExist() {
  try {
    const content = await fs.readFile(TOKEN_PATH);
    const credentials = JSON.parse(content);
    return google.auth.fromJSON(credentials);
  } catch (err) {
    return null;
  }
}

/**
 * Serializes credentials to a file compatible with GoogleAuth.fromJSON.
 * @param {OAuth2Client} client
 * @return {Promise<void>}
 */
export async function saveCredentials(client) {
  const content = await fs.readFile(CREDENTIALS_PATH);
  const keys = JSON.parse(content);
  const key = keys.installed || keys.web;
  const payload = JSON.stringify({
    type: 'authorized_user',
    client_id: key.client_id,
    client_secret: key.client_secret,
    refresh_token: client.credentials.refresh_token,
  });
  await fs.writeFile(TOKEN_PATH, payload);
}

/**
 * Get and store new token after prompting for user authorization, and then
 * execute the given callback with the authorized OAuth2 client.
 * @return {Promise<OAuth2Client>}
 */
export async function authorize() {
  let client = await loadSavedCredentialsIfExist();
  if (client) {
    return client;
  }
  client = await authenticate({
    scopes: SCOPES,
    keyfilePath: CREDENTIALS_PATH,
  });
  if (client.credentials) {
    await saveCredentials(client);
  }
  return client;
}

/**
 * Lists the labels in the user's account.
 * @param {google.auth.OAuth2} auth An authorized OAuth2 client.
 */
export async function listLabels(auth) {
  const gmail = google.gmail({ version: 'v1', auth });
  const res = await gmail.users.labels.list({
    userId: 'me',
  });
  const labels = res.data.labels;
  if (!labels || labels.length === 0) {
    console.log('No labels found.');
    return []; // Return empty array if no labels
  }
  console.log('Labels:');
  labels.forEach((label) => {
    console.log(`- ${label.name} (ID: ${label.id})`); // Also log ID for completeness
  });
  return labels.map(label => ({ name: label.name, id: label.id })); // Return array of label objects
}

/**
 * Lists messages in the user's account.
 * You can add queries to filter messages (e.g., 'in:inbox', 'subject:important')
 * @param {google.auth.OAuth2} auth An authorized OAuth2 client.
 * @param {string} query An optional query string to filter messages.
 * @param {number} maxResults Optional maximum number of messages to return.
 */
export async function listMessages(auth, query = '', maxResults = 10) {
  const gmail = google.gmail({ version: 'v1', auth });
  try {
    const res = await gmail.users.messages.list({
      userId: 'me',
      q: query, // e.g., 'in:inbox is:unread'
      maxResults: maxResults // Limit the number of messages
    });

    const messages = res.data.messages;
    if (!messages || messages.length === 0) {
      console.log('No messages found.');
      return [];
    }

    console.log('Messages:');
    const messageDetails = [];
    for (const message of messages) {
      const msgRes = await gmail.users.messages.get({
        userId: 'me',
        id: message.id,
        format: 'full' // or 'metadata', 'minimal', 'raw'
      });

      const payload = msgRes.data.payload;
      const headers = payload.headers;
      const subject = headers.find(header => header.name === 'Subject')?.value;
      const from = headers.find(header => header.name === 'From')?.value;
      const date = headers.find(header => header.name === 'Date')?.value;

      let body = '';
      if (payload.parts) {
        // Handle multipart messages
        const textPart = payload.parts.find(part => part.mimeType === 'text/plain');
        if (textPart && textPart.body && textPart.body.data) {
          body = Buffer.from(textPart.body.data, 'base64').toString('utf-8');
        }
      } else if (payload.body && payload.body.data) {
        // Handle single part messages
        body = Buffer.from(payload.body.data, 'base64').toString('utf-8');
      }

      console.log(`--- Message ID: ${message.id} ---`);
      console.log(`From: ${from}`);
      console.log(`Subject: ${subject}`);
      console.log(`Date: ${date}`);
      console.log(`Body (snippet): ${body.substring(0, 200)}...`); // Show first 200 chars of body
      messageDetails.push({ id: message.id, from, subject, date, body });
    }
    return messageDetails;

  } catch (err) {
    console.error('Error listing messages:', err);
    return [];
  }
}

// The following block is commented out as this file will now be used as a module.
// The calling code (e.g., the LLM tool) will be responsible for invoking these functions.
/*
authorize().then(async (authClient) => {
  await listLabels(authClient);
  console.log('\n--- Listing Inbox Messages ---');
  await listMessages(authClient, 'in:inbox'); // Get messages from the inbox
}).catch(console.error);
*/

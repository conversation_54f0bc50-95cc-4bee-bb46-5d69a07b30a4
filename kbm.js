/**
 * Knowledge Base Manager with Ollama Embeddings + Pinecone Integration
 * 
 * Features:
 * - 768-dimension embeddings using nomic-embed-text
 * - Automatic Pinecone index management
 * - File-based knowledge base with vector search
 */

import dotenv from 'dotenv';
dotenv.config();

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import crypto from 'crypto';
import { Pinecone } from '@pinecone-database/pinecone';
import { generateEmbedding, generateEmbeddingsBatch } from './services/embeddingService.js';

// Environment Validation
if (!process.env.OLLAMA_BASE_URL) {
  console.warn("OLLAMA_BASE_URL not set - using default localhost");
}
if (!process.env.PINECONE_API_KEY) {
  console.warn("PINECONE_API_KEY not set - Pinecone functionality will be disabled");
}

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const PINECONE_INDEX_NAME = 'kb';
const EMBEDDING_DIMENSION = 768;
const MAX_CONTENT_LENGTH = 32000; // ~8k tokens heuristic
const BASE_DIR = path.join(__dirname, 'knowledge_base');
const CONFIG_DIR = path.join(BASE_DIR, 'configurations');
const NOTES_DIR = path.join(BASE_DIR, 'notes');
const SNIPPETS_DIR = path.join(BASE_DIR, 'snippets');

let pinecone;
let pineconeIndex;

// Initialize Pinecone with retry logic
async function initPinecone(maxRetries = 3) {
  if (pineconeIndex) return;
  if (!process.env.PINECONE_API_KEY) {
    console.warn("Pinecone disabled - no API key configured");
    return;
  }

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      pinecone = new Pinecone({ apiKey: process.env.PINECONE_API_KEY });
      
      // Check/Create index
      const indexList = await pinecone.listIndexes();
      if (!indexList.indexes?.some(index => index.name === PINECONE_INDEX_NAME)) {
        console.log(`Creating Pinecone index "${PINECONE_INDEX_NAME}"...`);
        await pinecone.createIndex({
          name: PINECONE_INDEX_NAME,
          dimension: EMBEDDING_DIMENSION,
          metric: 'cosine',
          spec: {
            serverless: {
              cloud: 'aws',
              region: 'us-east-1'
            }
          }
        });
        console.log("Index created. Waiting for initialization...");
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10s for index init
      }

      pineconeIndex = pinecone.Index(PINECONE_INDEX_NAME);
      console.log(`Connected to Pinecone index "${PINECONE_INDEX_NAME}"`);
      return;
    } catch (error) {
      console.error(`Pinecone init attempt ${attempt} failed:`, error);
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
    }
  }
  throw new Error("Failed to initialize Pinecone after multiple attempts");
}

// Directory management
async function ensureBaseDirectories() {
  try {
    await fs.mkdir(BASE_DIR, { recursive: true });
    await fs.mkdir(CONFIG_DIR, { recursive: true });
    await fs.mkdir(NOTES_DIR, { recursive: true });
    await fs.mkdir(SNIPPETS_DIR, { recursive: true });
    await initPinecone();
    await warmupModel();
  } catch (error) {
    console.error("Initialization error:", error);
    throw error;
  }
}

// Model warmup
async function warmupModel() {
  try {
    console.time("model_warmup");
    await generateEmbedding("warmup");
    console.timeEnd("model_warmup");
  } catch (error) {
    console.error("Model warmup failed:", error);
  }
}

// Core functions
async function addEntry(type, name, content, metadata = null) {
  // Validate input
  if (content.length > MAX_CONTENT_LENGTH) {
    throw new Error(`Content exceeds ${MAX_CONTENT_LENGTH} character limit`);
  }

  await ensureBaseDirectories();
  
  // Determine target directory
  const dirs = {
    configuration: CONFIG_DIR,
    note: NOTES_DIR,
    snippet: SNIPPETS_DIR
  };
  const targetDir = dirs[type];
  if (!targetDir) throw new Error(`Invalid type: ${type}`);

  // Write file
  const filePath = path.join(targetDir, name);
  await fs.writeFile(filePath, content, 'utf8');

  // Write metadata if applicable
  if (type === 'snippet' && metadata) {
    const metaPath = `${filePath}.meta.json`;
    await fs.writeFile(metaPath, JSON.stringify(metadata, null, 2), 'utf8');
  }

  // Upsert to Pinecone
  if (pineconeIndex) {
    try {
      const fileHash = crypto.createHash('sha256').update(content).digest('hex');
      const pineconeId = `${type}-${fileHash.substring(0,8)}-${name}`;
      
      const embedding = await generateEmbedding(content);
      const pineconeMetadata = {
        type,
        name,
        fullContent: content.length > 1000 ? null : content,
        ...(type === 'snippet' && metadata ? {
          description: metadata.description,
          tags: metadata.tags,
          related: metadata.related_configs
        } : {})
      };

      await pineconeIndex.upsert([{
        id: pineconeId,
        values: embedding,
        metadata: pineconeMetadata
      }]);
    } catch (error) {
      console.error("Pinecone upsert failed:", error);
    }
  }

  return filePath;
}

async function searchEntries(keywords, searchType = null) {
  await ensureBaseDirectories();
  
  if (!keywords?.trim()) return [];
  const query = keywords.trim();

  // Vector search
  if (pineconeIndex) {
    try {
      console.time("vector_search");
      const embedding = await generateEmbedding(query);
      const response = await pineconeIndex.query({
        vector: embedding,
        topK: 10,
        includeMetadata: true,
        filter: searchType ? { type: searchType } : undefined
      });

      const results = response.matches
        .filter(match => match.metadata?.type && match.metadata?.name)
        .map(match => ({
          name: match.metadata.name,
          type: match.metadata.type,
          score: match.score,
          context: match.metadata.fullContent?.substring(0, 200) || "No preview",
          relativePath: `${match.metadata.type}s/${match.metadata.name}`
        }));

      console.timeEnd("vector_search");
      if (results.length > 0) return results;
    } catch (error) {
      console.error("Vector search failed:", error);
    }
  }

  // Fallback to lexical search
  return performLexicalSearch(query, searchType);
}

async function performLexicalSearch(query, type = null) {
  const terms = query.toLowerCase().split(/\s+/);
  const typesToSearch = type ? [type] : ['configuration', 'note', 'snippet'];
  const results = [];

  for (const currentType of typesToSearch) {
    const dir = path.join(BASE_DIR, `${currentType}s`);
    try {
      const files = await fs.readdir(dir);
      for (const file of files) {
        if (currentType === 'snippet' && file.endsWith('.meta.json')) continue;
        
        const content = await fs.readFile(path.join(dir, file), 'utf8');
        const searchable = `${file} ${content}`.toLowerCase();
        if (terms.every(term => searchable.includes(term))) {
          results.push({
            name: file,
            type: currentType,
            relativePath: `${currentType}s/${file}`,
            context: content.substring(0, 200)
          });
        }
      }
    } catch (error) {
      if (error.code !== 'ENOENT') console.error("Search error:", error);
    }
  }
  return results;
}

// Export public interface
export default {
  init: ensureBaseDirectories,
  addEntry,
  getEntry: async (type, name) => {
    const dir = `${type}s`;
    const content = await fs.readFile(path.join(BASE_DIR, dir, name), 'utf8');
    let result = { content };
    if (type === 'snippet') {
      try {
        result.metadata = JSON.parse(
          await fs.readFile(path.join(BASE_DIR, dir, `${name}.meta.json`), 'utf8')
        );
      } catch {/* ignore missing metadata */}
    }
    return result;
  },
  listEntries: async (type = null) => {
    const types = type ? [type] : ['configuration', 'note', 'snippet'];
    let files = [];
    for (const t of types) {
      try {
        const dirFiles = await fs.readdir(path.join(BASE_DIR, `${t}s`));
        files.push(...dirFiles.filter(f => !f.endsWith('.meta.json')).map(f => `${t}s/${f}`));
      } catch {/* ignore missing dirs */}
    }
    return files;
  },
  searchEntries
};
// searchTool.js

import { searchEngines, maxSearchRetries, excludedDomains, cacheExpiration } from './search_tool/config.js';
import { fetchContent } from './search_tool/fetcher.js';
import { extractSearchResults, extractMainContent } from './search_tool/parser.js';
import { retry, createCache, cleanWhitespace, isValidUrl } from './search_tool/utils.js';

class SearchTool {
    constructor() {
        this.name = "search";
        this.description = "A tool to search the internet for up-to-date information.";
        this.usage = "[TOOL: search(query)] where 'query' is a search term."; // Minor correction to description
        this.inputSchema = { // Renamed from parameters
            type: "object",
            properties: {
                query: { type: "string", description: "The search query." }
            },
            required: ["query"]
        };
        this.searchResultsCache = createCache(cacheExpiration);
        this.contentCache = createCache(cacheExpiration);
    }

    async performSearch(query, nluAnalysis = null) { // Added nluAnalysis parameter
        if (!query || typeof query !== 'string') {
            throw new Error('Invalid search query.');
        }

        let searchQuery = query;
        if (nluAnalysis && nluAnalysis.entities) {
            // Basic refinement: append key entities to the query. More sophisticated logic can be added later.
            // Example: if entities are { person: ["Kendrick Lamar"], topic: ["new album"] }
            // query might become: "resent new on kendrick lamar new album"
            // This is a simple example; real refinement might be more complex.
            const entityValues = Object.values(nluAnalysis.entities).flat().join(" ");
            if (entityValues) {
                searchQuery = `${query} ${entityValues}`;
                console.log(`🔎 [SEARCH ENGINE] Refined search query with NLU entities: "${searchQuery}"`);
            }
        }

        console.log(`🔎 [SEARCH ENGINE] Performing search for: "${searchQuery}" (Original: "${query}")`);

        // Use searchQuery for caching to reflect the refined query
        const cachedResults = this.searchResultsCache.get(searchQuery);
        if (cachedResults) {
            console.log(`🔎 [SEARCH ENGINE] Using cached results for: "${query}"`);
            return cachedResults;
        }

        const allResults = [];
        for (const engine of searchEngines) {
            try {
                const searchUrl = engine.url + encodeURIComponent(query);
                console.log(`🔎 [SEARCH ENGINE] Querying ${engine.name} at: ${searchUrl}`);

                const response = await retry(() => fetchContent(searchUrl), maxSearchRetries, 500);
                if (!response) {
                    console.log(`🔎 [SEARCH ENGINE] No response from ${engine.name}`);
                    continue;
                }

                let results = [];
                if (engine.isHtml) {
                    results = extractSearchResults(response.text, engine.name);
                    console.log(`🔎 [SEARCH ENGINE] Extracted ${results.length} results from ${engine.name} HTML`);
                } else if (response.json?.results) {
                    results = response.json.results.map(r => ({
                        url: r.url,
                        title: cleanWhitespace(r.title),
                        snippet: cleanWhitespace(r.snippet),
                        source: engine.name,
                    })).filter(r => isValidUrl(r.url));
                    console.log(`🔎 [SEARCH ENGINE] Extracted ${results.length} results from ${engine.name} JSON`);
                }

                const filteredResults = results.filter(res => !excludedDomains.some(d => res.url.includes(d)));
                console.log(`🔎 [SEARCH ENGINE] Added ${filteredResults.length} results from ${engine.name} after filtering`);
                allResults.push(...filteredResults);

            } catch (error) {
                console.error(`🔎 [SEARCH ENGINE] Search with ${engine.name} failed:`, error);
            }
        }

        const uniqueResults = Array.from(new Map(allResults.map(r => [r.url, r])).values()).slice(0, 5); //  Unique, top 5

        console.log(`🔎 [SEARCH ENGINE] Returning ${uniqueResults.length} unique results for: "${searchQuery}"`);
        if (uniqueResults.length > 0) {
            console.log(`🔎 [SEARCH ENGINE] First result: "${uniqueResults[0].title}"`);
        } else {
            console.log(`🔎 [SEARCH ENGINE] No results found for: "${searchQuery}"`);
        }

        this.searchResultsCache.set(searchQuery, uniqueResults); // Cache based on refined query
        return uniqueResults;
    }

    async fetchAndProcessContent(results) {
        console.log(`🔎 [SEARCH ENGINE] Fetching and processing content for ${results.length} results concurrently`);

        const enrichedResultsPromises = results.map(async (result) => {
            console.log(`🔎 [SEARCH ENGINE] Processing result: "${result.title}" (${result.url})`);
            let content = this.contentCache.get(result.url);

            if (content) {
                console.log(`🔎 [SEARCH ENGINE] Using cached content for: ${result.url}`);
            } else {
                try {
                    console.log(`🔎 [SEARCH ENGINE] Fetching content from: ${result.url}`);
                    const fetched = await fetchContent(result.url); // fetchContent likely needs to handle its own retries/timeouts

                    if (fetched?.isHtml) {
                        content = extractMainContent(fetched.text);
                        console.log(`🔎 [SEARCH ENGINE] Extracted ${content.length} characters of content from HTML for ${result.url}`);
                        if (content.length > 0) { // Only cache if content was actually extracted
                           this.contentCache.set(result.url, content);
                        }
                    } else if (fetched?.json) {
                        content = JSON.stringify(fetched.json);
                        console.log(`🔎 [SEARCH ENGINE] Extracted JSON content for ${result.url}`);
                        this.contentCache.set(result.url, content);
                    } else {
                        content = 'Non-HTML content or failed to fetch';
                        console.log(`🔎 [SEARCH ENGINE] Failed to extract content or non-HTML content for ${result.url}`);
                    }
                } catch (error) {
                    console.error(`🔎 [SEARCH ENGINE] Error fetching/processing content for ${result.url}:`, error);
                    content = `Error fetching content: ${error.message}`;
                }
            }
            return { ...result, content };
        });

        const enrichedResults = await Promise.all(enrichedResultsPromises);

        console.log(`🔎 [SEARCH ENGINE] Returning ${enrichedResults.length} enriched results after concurrent processing`);
        return enrichedResults;
    }

    // Expects a query string and optional NLU analysis object
    async execute(query, nluAnalysis = null) {
        if (nluAnalysis) {
            console.log(`🔎 [SEARCH ENGINE] Received NLU analysis:`, JSON.stringify(nluAnalysis, null, 2));
        }
        console.log(`🔎 [SEARCH ENGINE] Starting search process for query: "${query}"`);

        try {
            // Pass nluAnalysis to performSearch
            const results = await this.performSearch(query, nluAnalysis);
            const enrichedResults = await this.fetchAndProcessContent(results);

            console.log(`🔎 [SEARCH ENGINE] Search completed successfully for query: "${query}"`);
            return enrichedResults;
        } catch (error) {
            console.error(`🔎 [SEARCH ENGINE] Search failed for query: "${query}"`, error);
            return [{
                title: 'Search Error',
                snippet: `Failed to perform search: ${error.message}`,
                url: '#',
                content: 'Search engine error'
            }];
        }
    }

    getCommandsDocumentation() {
        // The Agent's parseToolCall expects <tool_name>:<command_name> <arg1> ...
        // The 'search' tool has one primary command 'execute'.
        // The inputSchema defines 'query' as the argument.
        return `- execute [query]: ${this.description} The query can be multiple words and should be enclosed in quotes if it contains spaces. (Example: TOOL_CALL: search: execute "latest AI news")`;
    }
}

// Export an instance of the SearchTool as a named export
export const searchTool = new SearchTool();

import { GoogleGenAI } from '@google/genai'; // Match GeminiProvider.js import
import kbToolInstance from './tools/kbTool.js';

// Constants for Knowledge Base tool function names
const KB_TOOL_FUNCTIONS = {
  KB_GET_ENTRY: 'kb_get_entry',
  KB_LIST_ENTRIES: 'kb_list_entries',
  KB_SEARCH_ENTRIES: 'kb_search_entries',
  KB_ADD_ENTRY: 'kb_add_entry',
};

// Universal tool definition that works with both Gemini and Ollama
const kbToolDefinition = {
  // Gemini format
  functionDeclarations: [
    {
      name: KB_TOOL_FUNCTIONS.KB_GET_ENTRY,
      description: "Retrieves a specific entry (e.g., configuration, note, code snippet) from the knowledge base.",
      parameters: {
        type: "OBJECT",
        properties: {
          entry_type: { type: "STRING", description: "The type of the entry. Common types: 'configuration', 'note', 'snippet'." },
          entry_name: { type: "STRING", description: "The unique name or identifier of the entry." }
        },
        required: ["entry_type", "entry_name"]
      }
    },
    {
      name: KB_TOOL_FUNCTIONS.KB_LIST_ENTRIES,
      description: "Lists available entries in the knowledge base, optionally filtered by type.",
      parameters: {
        type: "OBJECT",
        properties: {
          entry_type: { type: "STRING", description: "Optional. The type of entries to list (e.g., 'configuration', 'note', 'snippet'). If omitted, lists all types." }
        }
      }
    },
    {
      name: KB_TOOL_FUNCTIONS.KB_SEARCH_ENTRIES,
      description: "Searches for entries in the knowledge base using keywords, optionally filtered by type.",
      parameters: {
        type: "OBJECT",
        properties: {
          keywords: { type: "STRING", description: "Keywords to search for. Multiple keywords can be space-separated." },
          entry_type: { type: "STRING", description: "Optional. The type of entries to search within (e.g., 'configuration', 'note', 'snippet')." }
        },
        required: ["keywords"]
      }
    },
    {
      name: KB_TOOL_FUNCTIONS.KB_ADD_ENTRY,
      description: "Adds a new entry to the knowledge base.",
      parameters: {
        type: "OBJECT",
        properties: {
          entry_type: { type: "STRING", description: "The type for the new entry (e.g., 'configuration', 'note', 'snippet')." },
          entry_name: { type: "STRING", description: "The unique name or identifier for the new entry." },
          content: { type: "STRING", description: "The main content/text of the new entry." },
          metadata: { type: "OBJECT", description: "Optional. Additional metadata (key-value pairs) for the entry. Should be a flat object or stringified JSON." }
        },
        required: ["entry_type", "entry_name", "content"]
      }
    }
  ],
  
  // Agent.js/Ollama format
  description: "Knowledge Base tool for managing entries, notes, configurations, and code snippets",
  inputSchema: {
    type: "object",
    properties: {
      action: {
        type: "string",
        enum: ["get", "list", "search", "add"],
        description: "Action to perform on the knowledge base"
      },
      entry_type: {
        type: "string",
        description: "Type of entry (configuration, note, snippet, etc.)"
      },
      entry_name: {
        type: "string",
        description: "Name/identifier of the entry"
      },
      keywords: {
        type: "string",
        description: "Keywords for searching entries"
      },
      content: {
        type: "string",
        description: "Content for new entries"
      },
      metadata: {
        type: "object",
        description: "Additional metadata for entries"
      }
    },
    required: ["action"]
  },
  execute: async function(args) {
    switch (args.action) {
      case 'get':
        return await kbToolInstance.get({ type: args.entry_type, name: args.entry_name });
      case 'list':
        return await kbToolInstance.list({ type: args.entry_type });
      case 'search':
        return await kbToolInstance.search({ keywords: args.keywords, type: args.entry_type });
      case 'add':
        return await kbToolInstance.add({ 
          type: args.entry_type, 
          name: args.entry_name, 
          content: args.content, 
          metadata: args.metadata 
        });
      default:
        throw new Error(`Unknown knowledge base action: ${args.action}`);
    }
  }
};

/**
 * A client for interacting with Large Language Models (LLM),
 * supporting both Google's Generative AI and Ollama.
 */
class LLMClient {
  constructor(config) {
    if (!config) {
      throw new Error('LLMClient requires a configuration object.');
    }

    this.provider = config.provider || 'gemini';
    this.modelName = config.modelName || this._getDefaultModel();
    
    // Initialize the appropriate provider
    if (this.provider === 'ollama') {
      this._initializeOllama(config);
    } else if (this.provider === 'gemini') {
      this._initializeGemini(config);
    } else {
      throw new Error(`Unsupported provider: ${this.provider}`);
    }

    // Set up tools
    this.tools = [kbToolDefinition];
    this.availableTools = {
      [KB_TOOL_FUNCTIONS.KB_GET_ENTRY]: async (args) => kbToolInstance.get({ type: args.entry_type, name: args.entry_name }),
      [KB_TOOL_FUNCTIONS.KB_LIST_ENTRIES]: async (args) => kbToolInstance.list({ type: args.entry_type }),
      [KB_TOOL_FUNCTIONS.KB_SEARCH_ENTRIES]: async (args) => kbToolInstance.search({ keywords: args.keywords, type: args.entry_type }),
      [KB_TOOL_FUNCTIONS.KB_ADD_ENTRY]: async (args) => kbToolInstance.add({ 
        type: args.entry_type, 
        name: args.entry_name, 
        content: args.content, 
        metadata: args.metadata 
      }),
      // Universal knowledge base tool executor
      'kb_tool': kbToolDefinition.execute
    };
  }

  _getDefaultModel() {
    return this.provider === 'ollama' ? 'gemma3:1b' : 'gemini-2.0-flash-lite';
  }

  _initializeOllama(config) {
    const baseUrl = config.baseUrl || 'http://localhost:11434';
    const defaultOptions = {
      temperature: 0.7,
      top_p: 0.95,
      top_k: 40,
      num_predict: 4096,
      ...config.generationConfig
    };
    
    this.ollama = new OllamaProvider(this.modelName, baseUrl, defaultOptions);
    this.defaultGenerationConfig = defaultOptions;
  }

  _initializeGemini(config) {
    if (!config.apiKey) {
      throw new Error('LLMClient requires an API key for Gemini provider.');
    }
    
    this.ai = new GoogleGenAI({ apiKey: config.apiKey });
    this.defaultGenerationConfig = config.generationConfig || {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 4096,
    };
  }

  /**
   * Generates content as a stream from the LLM, handling function calls.
   */
  async *generateContentStream({ modelName, history, generationConfig, tools, toolConfig }) {
    if (!history || !Array.isArray(history)) {
      throw new Error('history (array) is required for generateContentStream.');
    }

    const currentModelName = modelName || this.modelName;
    const currentGenerationConfig = generationConfig || this.defaultGenerationConfig;

    if (this.provider === 'ollama') {
      yield* this._generateOllamaContentStream({
        modelName: currentModelName,
        history,
        generationConfig: currentGenerationConfig,
        tools: tools || this.tools
      });
    } else {
      yield* this._generateGeminiContentStream({
        modelName: currentModelName,
        history,
        generationConfig: currentGenerationConfig,
        tools: tools || this.tools,
        toolConfig
      });
    }
  }

  async *_generateOllamaContentStream({ modelName, history, generationConfig, tools }) {
    // Convert history to Ollama format
    const messages = this._convertHistoryToOllama(history);
    
    // For Ollama, we'll simulate tool calling by parsing the response
    // This is a simplified approach since Ollama doesn't have native function calling
    const prompt = {
      history: messages,
      contents: []
    };

    try {
      let fullResponse = '';
      const stream = this.ollama.generateContentStream(prompt, generationConfig, (chunk) => {
        fullResponse += chunk;
      });

      const response = await stream;
      
      // Check if the response contains a tool call pattern
      const toolCallMatch = this._parseToolCallFromText(response);
      
      if (toolCallMatch) {
        // Execute the tool and get the result
        const toolResult = await this._executeToolCall(toolCallMatch);
        
        // Add tool interaction to history and generate follow-up
        const updatedMessages = [
          ...messages,
          { role: 'assistant', content: response },
          { role: 'system', content: `Tool result: ${JSON.stringify(toolResult)}` }
        ];

        const followUpPrompt = {
          history: updatedMessages,
          contents: []
        };

        const followUpResponse = await this.ollama.generateContent(followUpPrompt, generationConfig);
        yield { text: followUpResponse };
      } else {
        yield { text: response };
      }
    } catch (error) {
      console.error('Error in Ollama content generation:', error);
      throw error;
    }
  }

  async *_generateGeminiContentStream({ modelName, history, generationConfig, tools, toolConfig }) {
    const currentHistory = [...history];
    let processingFunctionCallCycle = true;

    while (processingFunctionCallCycle) {
      processingFunctionCallCycle = false;

      const streamResult = await this.ai.models.generateContentStream({
        model: modelName,
        contents: currentHistory,
        generationConfig: generationConfig,
        tools: tools,
        toolConfig: toolConfig || { functionCallingConfig: { mode: "AUTO" } }
      });
      
      let accumulatedFunctionCall = null;

      for await (const chunk of streamResult) {
        let fc = null;
        if (chunk.functionCall) {
          fc = chunk.functionCall;
        } else if (chunk.parts && chunk.parts[0] && chunk.parts[0].functionCall) {
          fc = chunk.parts[0].functionCall;
        }
        
        const functionCallsInChunk = fc ? [fc] : null;

        if (functionCallsInChunk && functionCallsInChunk.length > 0) {
          processingFunctionCallCycle = true;
          
          const currentFc = functionCallsInChunk[0];
          if (!accumulatedFunctionCall) {
            accumulatedFunctionCall = { name: currentFc.name, args: { ...(currentFc.args || {}) } };
          } else {
            Object.assign(accumulatedFunctionCall.args, (currentFc.args || {}));
          }
        } else {
          const textContent = chunk.candidates?.[0]?.content?.parts?.[0]?.text;
          if (textContent !== undefined && textContent !== null && textContent !== '') {
            yield { text: textContent };
          }
        }
      }

      if (processingFunctionCallCycle && accumulatedFunctionCall && accumulatedFunctionCall.name) {
        await this._handleGeminiFunctionCall(accumulatedFunctionCall, currentHistory);
        accumulatedFunctionCall = null;
      } else if (!processingFunctionCallCycle) {
        break;
      }
    }
  }

  async _handleGeminiFunctionCall(functionCall, history) {
    const functionName = functionCall.name;
    const functionArgs = functionCall.args;

    if (this.availableTools[functionName]) {
      console.log(`LLMClient: Calling tool function: ${functionName} with args:`, functionArgs);
      try {
        const toolResult = await this.availableTools[functionName](functionArgs);
        console.log(`LLMClient: Tool ${functionName} result:`, toolResult);

        history.push({
          role: 'model',
          parts: [{ functionCall: { name: functionName, args: functionArgs } }]
        });
        history.push({
          role: 'function',
          parts: [{ functionResponse: { name: functionName, response: toolResult } }]
        });
      } catch (error) {
        console.error(`LLMClient: Error executing tool ${functionName}:`, error);
        history.push({
          role: 'model',
          parts: [{ functionCall: { name: functionName, args: functionArgs } }]
        });
        history.push({
          role: 'function',
          parts: [{ functionResponse: { name: functionName, response: { error: `Error executing tool: ${error.message}` } } }]
        });
      }
    } else {
      console.warn(`LLMClient: Unknown tool function called: ${functionName}`);
      history.push({
        role: 'model',
        parts: [{ functionCall: { name: functionName, args: functionArgs } }]
      });
      history.push({
        role: 'function',
        parts: [{ functionResponse: { name: functionName, response: { error: `Tool ${functionName} not found.` } } }]
      });
    }
  }

  _convertHistoryToOllama(history) {
    return history.map(item => {
      if (item.parts) {
        const content = item.parts.map(part => part.text || '').join('\n');
        return {
          role: item.role === 'model' ? 'assistant' : item.role,
          content: content
        };
      }
      return {
        role: item.role === 'model' ? 'assistant' : item.role,
        content: item.content || ''
      };
    });
  }

  _parseToolCallFromText(text) {
    // Simple pattern matching for tool calls in text
    // This is a basic implementation - you might want to make this more sophisticated
    const patterns = [
      /kb_get_entry\s*\(([^)]+)\)/i,
      /kb_list_entries\s*\(([^)]*)\)/i,
      /kb_search_entries\s*\(([^)]+)\)/i,
      /kb_add_entry\s*\(([^)]+)\)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        try {
          const args = match[1] ? JSON.parse(`{${match[1]}}`) : {};
          return {
            name: match[0].split('(')[0].trim(),
            args: args
          };
        } catch (e) {
          console.warn('Failed to parse tool call arguments:', match[1]);
        }
      }
    }
    return null;
  }

  async _executeToolCall(toolCall) {
    const toolFunction = this.availableTools[toolCall.name];
    if (toolFunction) {
      return await toolFunction(toolCall.args);
    }
    throw new Error(`Unknown tool: ${toolCall.name}`);
  }

  /**
   * Generates content non-streamed from the LLM, handling function calls.
   */
  async generateContent({ modelName, history, generationConfig, tools, toolConfig }) {
    if (!history || !Array.isArray(history)) {
      throw new Error('history (array) is required for generateContent.');
    }

    const currentModelName = modelName || this.modelName;
    const currentGenerationConfig = generationConfig || this.defaultGenerationConfig;
    
    let fullReply = '';
    try {
      const stream = this.generateContentStream({
        modelName: currentModelName,
        history,
        generationConfig: currentGenerationConfig,
        tools: tools || this.tools,
        toolConfig: toolConfig || { functionCallingConfig: { mode: "AUTO" } }
      });
      
      for await (const chunk of stream) {
        if (chunk.text) {
          fullReply += chunk.text;
        }
      }
      return fullReply;
    } catch (error) {
      console.error('Error in LLMClient.generateContent:', error);
      throw error;
    }
  }

  /**
   * Get current provider information
   */
  getProviderInfo() {
    return {
      provider: this.provider,
      modelName: this.modelName,
      defaultConfig: this.defaultGenerationConfig
    };
  }

  /**
   * Switch provider (requires reinitialization)
   */
  switchProvider(newConfig) {
    this.provider = newConfig.provider || 'gemini';
    this.modelName = newConfig.modelName || this._getDefaultModel();
    
    if (this.provider === 'ollama') {
      this._initializeOllama(newConfig);
    } else if (this.provider === 'gemini') {
      this._initializeGemini(newConfig);
    } else {
      throw new Error(`Unsupported provider: ${this.provider}`);
    }
  }
}

export default LLMClient;
